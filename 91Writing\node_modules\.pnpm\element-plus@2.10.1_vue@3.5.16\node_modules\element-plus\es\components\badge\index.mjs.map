{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/badge/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Badge from './src/badge.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElBadge: SFCWithInstall<typeof Badge> = withInstall(Badge)\nexport default ElBadge\n\nexport * from './src/badge'\nexport type { BadgeInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}