# AI聊天框使用示例

## 🎯 功能演示

### 示例1：完整创作流程

**用户输入：**
```
我想写一部科幻小说，主角是一个黑客，在虚拟世界中冒险
```

**AI回复：**
```
根据您的描述，我为您推荐：

标题：《数字迷城》
类型：科幻
简介：在2045年的赛博世界中，天才黑客林墨意外发现了一个隐藏的虚拟迷城。在这个充满危险与机遇的数字世界里，他必须运用自己的技能突破重重关卡，揭开迷城背后的惊天秘密。

这个标题体现了虚拟世界的神秘感，科幻类型符合您的设定，简介突出了主角身份和核心冲突。
```

**系统行为：**
- 在标题字段下方显示：AI建议：《数字迷城》[采用]
- 在类型字段下方显示：AI建议：科幻 [采用]  
- 在简介字段下方显示：AI建议：在2045年的赛博世界中... [采用]

### 示例2：单项咨询

**用户输入：**
```
帮我想一个武侠小说的标题
```

**AI回复：**
```
根据武侠小说的特点，我推荐：

标题：《剑心无痕》

这个标题体现了武侠小说中剑客的精神境界，"无痕"暗示着高超的剑法和淡泊的心境，很符合武侠小说的意境。
```

**系统行为：**
- 在标题字段下方显示：AI建议：《剑心无痕》[采用]

### 示例3：基于已有信息的建议

**当前状态：**
- 标题：《星空下的约定》
- 类型：未选择
- 简介：未填写

**用户输入：**
```
帮我选择合适的小说类型
```

**AI回复：**
```
根据您的标题《星空下的约定》，我建议：

类型：言情

"星空下的约定"这个标题充满了浪漫色彩，暗示着美好的爱情承诺，非常适合言情小说的主题。
```

### 示例4：快捷按钮使用

**点击"想标题"按钮：**
- 如果已选择类型（如：都市），输入框自动填入："帮我想一个都市小说的标题"
- 如果未选择类型，输入框填入："帮我想一个小说标题"

**点击"选类型"按钮：**
- 如果已填写标题，输入框自动填入："我的小说标题是《xxx》，帮我选择合适的类型"
- 如果未填写标题，输入框填入："帮我选择合适的小说类型"

**点击"写简介"按钮：**
- 如果已有标题和类型，输入框填入："标题：《xxx》，类型：xxx，帮我写一个吸引人的简介"
- 如果只有部分信息，会根据已有信息生成相应提示

## 🔧 智能解析规则

### 标题识别模式
- `标题：《xxx》`
- `书名：《xxx》`
- `建议标题：《xxx》`
- `推荐标题：《xxx》`
- `叫《xxx》`
- `名为《xxx》`

### 类型识别模式
- `类型：xxx`
- `题材：xxx`
- `这是一部xxx小说`
- `属于xxx类型`
- `建议选择xxx`

### 简介识别模式
- `简介：xxx。`
- `内容简介：xxx。`
- `故事简介：xxx。`
- `讲述了xxx。`

## 💡 使用技巧

### 1. 描述要具体
❌ 不好的描述：
```
我想写小说
```

✅ 好的描述：
```
我想写一部都市言情小说，讲述一个程序员和设计师的爱情故事，背景设定在现代互联网公司
```

### 2. 分步骤咨询
可以先确定一个要素，再逐步完善：
```
第一步：我想写科幻小说
第二步：主角是时间旅行者
第三步：故事发生在平行宇宙
```

### 3. 利用快捷按钮
- 表单为空时，点击"全部填写"获得完整建议
- 有部分信息时，点击对应的快捷按钮获得针对性建议

### 4. 及时采用建议
- AI建议出现后，及时点击"采用"按钮
- 如果不满意，可以继续对话要求修改

## 🎨 界面说明

### 左侧表单区域
- **标题字段**：输入或查看建议的小说标题
- **类型下拉框**：选择或查看建议的小说类型
- **简介文本框**：输入或查看建议的小说简介
- **AI建议框**：显示在对应字段下方，紫色背景

### 右侧聊天区域
- **聊天历史**：显示与AI的对话记录
- **快捷按钮**：想标题、选类型、写简介、全部填写
- **输入框**：输入您的需求和问题
- **发送按钮**：发送消息（支持Ctrl+Enter快捷键）

## 🚀 高级用法

### 1. 多轮对话优化
```
用户：我想写玄幻小说
AI：[提供基础建议]
用户：主角要是女性，背景设定在古代
AI：[根据新信息调整建议]
用户：加入修仙元素
AI：[进一步优化建议]
```

### 2. 风格指定
```
用户：我想写一部轻松幽默的都市小说，不要太严肃
AI：[生成符合轻松风格的建议]
```

### 3. 参考作品
```
用户：我想写类似《哈利波特》风格的奇幻小说
AI：[参考经典作品特点生成建议]
```

## ⚠️ 注意事项

1. **AI建议仅供参考**：请根据自己的创作需求进行调整
2. **及时保存**：采用建议后记得保存表单
3. **网络要求**：需要稳定的网络连接
4. **API配置**：确保已正确配置AI API密钥
5. **内容审核**：AI生成的内容可能需要人工审核和调整

## 🔍 故障排除

### AI无响应
1. 检查网络连接
2. 验证API密钥配置
3. 查看浏览器控制台错误信息

### 建议不准确
1. 提供更详细的描述
2. 使用多轮对话细化需求
3. 手动调整AI建议的内容

### 界面显示异常
1. 刷新页面重试
2. 检查浏览器兼容性
3. 清除浏览器缓存

通过这个AI聊天框功能，您可以更轻松地创建小说项目，让AI成为您创作路上的得力助手！✨
