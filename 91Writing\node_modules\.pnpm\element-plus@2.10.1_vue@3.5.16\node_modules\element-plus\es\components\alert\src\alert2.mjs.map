{"version": 3, "file": "alert2.mjs", "sources": ["../../../../../../packages/components/alert/src/alert.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"visible\"\n      :class=\"[ns.b(), ns.m(type), ns.is('center', center), ns.is(effect)]\"\n      role=\"alert\"\n    >\n      <el-icon\n        v-if=\"showIcon && ($slots.icon || iconComponent)\"\n        :class=\"[ns.e('icon'), { [ns.is('big')]: hasDesc }]\"\n      >\n        <slot name=\"icon\">\n          <component :is=\"iconComponent\" />\n        </slot>\n      </el-icon>\n\n      <div :class=\"ns.e('content')\">\n        <span\n          v-if=\"title || $slots.title\"\n          :class=\"[ns.e('title'), { 'with-description': hasDesc }]\"\n        >\n          <slot name=\"title\">{{ title }}</slot>\n        </span>\n        <p v-if=\"hasDesc\" :class=\"ns.e('description')\">\n          <slot>\n            {{ description }}\n          </slot>\n        </p>\n        <template v-if=\"closable\">\n          <div\n            v-if=\"closeText\"\n            :class=\"[ns.e('close-btn'), ns.is('customed')]\"\n            @click=\"close\"\n          >\n            {{ closeText }}\n          </div>\n          <el-icon v-else :class=\"ns.e('close-btn')\" @click=\"onClose\">\n            <Close />\n          </el-icon>\n        </template>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, toRef, useSlots } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  TypeComponents,\n  TypeComponentsMap,\n  isClient,\n} from '@element-plus/utils'\nimport { useDelayedToggle, useNamespace } from '@element-plus/hooks'\nimport { alertEmits, alertProps } from './alert'\n\nconst { Close } = TypeComponents\n\ndefineOptions({\n  name: 'ElAlert',\n})\n\nconst props = defineProps(alertProps)\nconst emit = defineEmits(alertEmits)\nconst slots = useSlots()\n\nconst ns = useNamespace('alert')\n\nconst visible = ref(false)\n\nconst iconComponent = computed(() => TypeComponentsMap[props.type])\n\nconst hasDesc = computed(() => !!(props.description || slots.default))\n\nconst open = () => {\n  visible.value = true\n  emit('open')\n}\n\nconst close = (event?: Event) => {\n  visible.value = false\n  emit('close', event)\n}\n\nconst { onOpen, onClose } = useDelayedToggle({\n  showAfter: toRef(props, 'showAfter'),\n  hideAfter: toRef(props, 'hideAfter'),\n  autoClose: toRef(props, 'autoClose'),\n  open,\n  close,\n})\n\nif (isClient) {\n  onOpen()\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;mCA0Dc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAM,MAAA,EAAE,OAAU,GAAA,cAAA,CAAA;AAQlB,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAE/B,IAAM,MAAA,OAAA,GAAU,IAAI,KAAK,CAAA,CAAA;AAEzB,IAAA,MAAM,gBAAgB,QAAS,CAAA,MAAM,iBAAkB,CAAA,KAAA,CAAM,IAAI,CAAC,CAAA,CAAA;AAElE,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM,CAAC,EAAE,KAAM,CAAA,WAAA,IAAe,MAAM,OAAQ,CAAA,CAAA,CAAA;AAErE,IAAA,MAAM,OAAO,MAAM;AACjB,MAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AAChB,MAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AAAA,KACb,CAAA;AAEA,IAAM,MAAA,KAAA,GAAQ,CAAC,KAAkB,KAAA;AAC/B,MAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA,CAAA;AAChB,MAAA,IAAA,CAAK,SAAS,KAAK,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAA,MAAM,EAAE,MAAA,EAAQ,OAAQ,EAAA,GAAI,gBAAiB,CAAA;AAAA,MAC3C,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,IAAA;AAAA,MACA,KAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,IAAI,QAAU,EAAA;AACZ,MAAO,MAAA,EAAA,CAAA;AAAA,KACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}