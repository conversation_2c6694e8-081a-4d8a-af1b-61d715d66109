# 测试AI聊天框功能

## 测试步骤

### 1. 基础功能测试

1. **打开创建小说对话框**
   - 访问 http://localhost:7550/
   - 点击左侧菜单"小说列表"
   - 点击"创建新小说"按钮
   - 验证对话框是否显示为左右分栏布局

2. **聊天界面测试**
   - 验证右侧是否显示AI助手区域
   - 检查是否有"AI创作助手"标题
   - 确认有"清空"按钮
   - 验证聊天历史区域显示提示文字

3. **快捷按钮测试**
   - 点击"想标题"按钮，验证输入框是否自动填入提示文字
   - 点击"选类型"按钮，验证输入框是否自动填入提示文字
   - 点击"写简介"按钮，验证输入框是否自动填入提示文字

### 2. AI对话测试

**前提条件：需要先配置API密钥**
1. 点击右上角设置按钮
2. 进入API配置页面
3. 添加有效的API密钥

**测试对话：**
1. 在聊天输入框输入："我想写一部都市言情小说"
2. 点击发送或按Ctrl+Enter
3. 验证：
   - 用户消息是否正确显示（蓝色气泡，右对齐）
   - 是否显示"AI正在思考"的加载状态
   - AI回复是否正确显示（白色气泡，左对齐）
   - 时间戳是否正确显示

### 3. 自动填表测试

1. **标题自动填写测试**
   - 输入："帮我想一个标题叫《星空下的约定》"
   - 验证左侧表单的标题字段是否自动填入
   - 验证是否显示成功提示消息

2. **类型自动选择测试**
   - 输入："这是一部言情小说"
   - 验证左侧表单的类型下拉框是否自动选择
   - 验证是否显示成功提示消息

3. **简介自动填写测试**
   - 输入："简介：这是一个关于爱情的故事。"
   - 验证左侧表单的简介字段是否自动填入
   - 验证是否显示成功提示消息

### 4. 交互功能测试

1. **对话历史测试**
   - 发送多条消息
   - 验证对话历史是否正确保存和显示
   - 验证滚动是否自动到底部

2. **清空功能测试**
   - 点击"清空"按钮
   - 验证对话历史是否被清空
   - 验证是否恢复到初始状态

3. **键盘快捷键测试**
   - 在输入框中输入内容
   - 按Ctrl+Enter发送
   - 验证消息是否正确发送

### 5. 错误处理测试

1. **无API配置测试**
   - 清空API配置
   - 尝试发送消息
   - 验证是否显示正确的错误提示

2. **网络错误测试**
   - 断开网络连接
   - 尝试发送消息
   - 验证是否显示网络错误提示

3. **空消息测试**
   - 尝试发送空消息
   - 验证发送按钮是否被禁用
   - 验证不会发送空消息

### 6. 样式和布局测试

1. **响应式布局测试**
   - 调整浏览器窗口大小
   - 验证布局是否正确适应

2. **动画效果测试**
   - 发送消息时验证淡入动画
   - 验证打字指示器动画效果

3. **滚动测试**
   - 发送多条消息直到超出可视区域
   - 验证滚动条是否正常工作
   - 验证新消息是否自动滚动到底部

## 预期结果

### 成功标准
- ✅ 所有UI元素正确显示
- ✅ AI对话功能正常工作
- ✅ 自动填表功能正确执行
- ✅ 错误处理机制有效
- ✅ 用户体验流畅

### 常见问题解决

1. **对话框布局异常**
   - 检查CSS样式是否正确加载
   - 验证浏览器兼容性

2. **AI无法回应**
   - 检查API配置是否正确
   - 验证网络连接状态
   - 查看浏览器控制台错误信息

3. **自动填表不工作**
   - 检查AI回复格式是否符合解析规则
   - 验证表单字段是否为空（不覆盖已有内容）

## 测试环境

- **浏览器**：Chrome 90+, Firefox 88+, Safari 14+
- **分辨率**：1920x1080, 1366x768, 移动端
- **网络**：正常网络连接
- **API**：有效的AI API密钥

## 测试报告模板

```
测试日期：____
测试人员：____
浏览器版本：____

基础功能：□ 通过 □ 失败
AI对话：□ 通过 □ 失败
自动填表：□ 通过 □ 失败
交互功能：□ 通过 □ 失败
错误处理：□ 通过 □ 失败
样式布局：□ 通过 □ 失败

问题记录：
1. ________________
2. ________________
3. ________________

总体评价：□ 优秀 □ 良好 □ 需改进
```
