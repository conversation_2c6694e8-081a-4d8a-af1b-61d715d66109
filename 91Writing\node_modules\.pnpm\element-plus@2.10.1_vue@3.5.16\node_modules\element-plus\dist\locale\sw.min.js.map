{"version": 3, "file": "sw.min.js", "sources": ["../../../../packages/locale/lang/sw.ts"], "sourcesContent": ["export default {\n  name: 'sw',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'sawa',\n      clear: 'futa',\n      defaultLabel: 'kichagua rangi',\n      description:\n        'rangi ya sasa ni {color}. bonyeza kitufe cha kuingia ili kuchagua rangi mpya.',\n    },\n    datepicker: {\n      now: 'sasa',\n      today: 'leo',\n      cancel: 'kufuta',\n      clear: 'futa',\n      confirm: 'sawa',\n      dateTablePrompt:\n        'Tumia vitufe vya vishale na uweke ili kuchagua siku katika mwezi',\n      monthTablePrompt:\n        'Tumia vitufe vya vishale na uingize ili kuchagua mwezi',\n      yearTablePrompt: 'Tumia vitufe vya vishale na uingize ili kuchagua mwaka',\n      selectedDate: 'tarehe iliyochaguliwa',\n      selectDate: 'chagua tarehe',\n      selectTime: 'chagua muda',\n      startDate: 'siku ya kuanza',\n      startTime: 'muda wa kuanza',\n      endDate: 'tarehe ya mwisho',\n      endTime: 'wakati wa mwisho',\n      prevYear: 'mwaka uliopita',\n      nextYear: 'mwaka ujao',\n      prevMonth: 'mwezi uliopita',\n      nextMonth: 'mwezi ujao',\n      year: '',\n      month1: 'mwezi wa kwanza',\n      month2: 'mwezi wa pili',\n      month3: 'mwezi tatu',\n      month4: 'mwezi wa nne',\n      month5: 'Mwezi wa tano',\n      month6: 'mwezi wa sita',\n      month7: 'mwezi wa saba',\n      month8: 'mwezi wa nane',\n      month9: 'mwezi wa tisa',\n      month10: 'mwezi wa kumi',\n      month11: 'mwezi wa kumi na moja',\n      month12: 'mwezi wa kumi na mbili',\n      week: 'siku saba',\n      weeks: {\n        sun: 'jpili',\n        mon: 'jtatu',\n        tue: 'jnne',\n        wed: 'jtano',\n        thu: 'alh',\n        fri: 'jumaa',\n        sat: 'jmosi',\n      },\n      weeksFull: {\n        sun: 'jumapili',\n        mon: 'jumatatu',\n        tue: 'jumanne',\n        wed: 'jumatano',\n        thu: 'alhamisi',\n        fri: 'ijumaaa',\n        sat: 'jumamosi',\n      },\n      months: {\n        jan: 'mwezi 1',\n        feb: 'mwezi 2',\n        mar: 'mwezi 3',\n        apr: 'mwezi 4',\n        may: 'mwezi 5',\n        jun: 'mwezi 6',\n        jul: 'mwezi 7',\n        aug: 'mwezi 8',\n        sep: 'mwezi 9',\n        oct: 'mwezi 10',\n        nov: 'mwezi 11',\n        dec: 'mwezi 12',\n      },\n    },\n    inputNumber: {\n      decrease: 'kupunguza idadi',\n      increase: 'kuongeza idadi',\n    },\n    select: {\n      loading: 'inapakia',\n      noMatch: 'hakuna data inayolingana',\n      noData: 'hakuna data',\n      placeholder: 'chagua',\n    },\n    mention: {\n      loading: 'inapakia',\n    },\n    dropdown: {\n      toggleDropdown: 'geuza kunyuzi',\n    },\n    cascader: {\n      noMatch: 'hakuna data inayolingana',\n      loading: 'pakia',\n      placeholder: 'chagua',\n      noData: 'hakuna data',\n    },\n    pagination: {\n      goto: 'enda kwenye',\n      pagesize: '/kurasa',\n      total: 'jumla {total}',\n      pageClassifier: '',\n      page: 'kurasa',\n      prev: 'Nenda kwenye ukurasa uliopita',\n      next: 'Nenda kwenye ukurasa unaofuata',\n      currentPage: 'kurasa {pager}',\n      prevPages: 'Kurasa za {pager} zilizopita',\n      nextPages: 'Kurasa {pager} zinazofuata',\n      deprecationWarning:\n        'Matumizi yaliyoacha kutumika yamegunduliwa, tafadhali rejelea hati za el-pagination kwa maelezo zaidi',\n    },\n    dialog: {\n      close: 'funga kidirisha hiki',\n    },\n    drawer: {\n      close: 'funga kidirisha hiki',\n    },\n    messagebox: {\n      title: 'ujumbe',\n      confirm: 'sawa',\n      cancel: 'futa',\n      error: 'Uingizaji haramu',\n      close: 'Funga kidirisha hiki',\n    },\n    upload: {\n      deleteTip: 'bonyeza kufuta ili kuondoa',\n      delete: 'kufuta',\n      preview: 'hakikisho',\n      continue: 'endelea',\n    },\n    slider: {\n      defaultLabel: 'kitelelzi kati ya {min} na {max}',\n      defaultRangeStartLabel: 'cahgua thamani ya kuanzia',\n      defaultRangeEndLabel: 'chagua thamani ya mwisho',\n    },\n    table: {\n      emptyText: 'hakuna data',\n      confirmFilter: 'thibitisha',\n      resetFilter: 'weka upya',\n      clearFilter: 'zote',\n      sumText: 'jumla',\n    },\n    tree: {\n      emptyText: 'hakuna data',\n    },\n    transfer: {\n      noMatch: 'hakuna data inayolingana',\n      noData: 'hakuna data',\n      titles: ['orodha ya kwanza', 'orodha ya pili'],\n      filterPlaceholder: 'Ingiza neno kuu',\n      noCheckedFormat: '{total} vitu',\n      hasCheckedFormat: '{checked}/{total} imechaguliwa',\n    },\n    image: {\n      error: 'imeshindwa',\n    },\n    pageHeader: {\n      title: 'nyuma',\n    },\n    popconfirm: {\n      confirmButtonText: 'ndio',\n      cancelButtonText: 'hapana',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,CAAC,+EAA+E,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,kEAAkE,CAAC,gBAAgB,CAAC,wDAAwD,CAAC,eAAe,CAAC,wDAAwD,CAAC,YAAY,CAAC,uBAAuB,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,gCAAgC,CAAC,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,8BAA8B,CAAC,SAAS,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,uGAAuG,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,kCAAkC,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}