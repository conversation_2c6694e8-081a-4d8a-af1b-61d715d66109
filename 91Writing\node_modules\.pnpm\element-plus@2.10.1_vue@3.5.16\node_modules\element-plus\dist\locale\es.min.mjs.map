{"version": 3, "file": "es.min.mjs", "sources": ["../../../../packages/locale/lang/es.ts"], "sourcesContent": ["export default {\n  name: 'es',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Confirmar',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Ahora',\n      today: 'Hoy',\n      cancel: 'Cancelar',\n      clear: '<PERSON><PERSON>jar',\n      confirm: 'Confirmar',\n      selectDate: 'Seleccionar fecha',\n      selectTime: 'Seleccionar hora',\n      startDate: 'Fecha Incial',\n      startTime: 'Hora Inicial',\n      endDate: 'Fecha Final',\n      endTime: 'Hora Final',\n      prevYear: 'Año Anterior',\n      nextYear: 'Próximo Año',\n      prevMonth: 'Mes Anterior',\n      nextMonth: 'Próximo Me<PERSON>',\n      year: '',\n      month1: 'enero',\n      month2: 'febrero',\n      month3: 'marzo',\n      month4: 'abril',\n      month5: 'mayo',\n      month6: 'junio',\n      month7: 'julio',\n      month8: 'agosto',\n      month9: 'septiembre',\n      month10: 'octubre',\n      month11: 'noviembre',\n      month12: 'diciembre',\n      // week: 'semana',\n      weeks: {\n        sun: 'dom',\n        mon: 'lun',\n        tue: 'mar',\n        wed: 'mié',\n        thu: 'jue',\n        fri: 'vie',\n        sat: 'sáb',\n      },\n      months: {\n        jan: 'ene',\n        feb: 'feb',\n        mar: 'mar',\n        apr: 'abr',\n        may: 'may',\n        jun: 'jun',\n        jul: 'jul',\n        aug: 'ago',\n        sep: 'sep',\n        oct: 'oct',\n        nov: 'nov',\n        dec: 'dic',\n      },\n    },\n    select: {\n      loading: 'Cargando',\n      noMatch: 'No hay datos que coincidan',\n      noData: 'Sin datos',\n      placeholder: 'Seleccionar',\n    },\n    mention: {\n      loading: 'Cargando',\n    },\n    cascader: {\n      noMatch: 'No hay datos que coincidan',\n      loading: 'Cargando',\n      placeholder: 'Seleccionar',\n      noData: 'Sin datos',\n    },\n    pagination: {\n      goto: 'Ir a',\n      pagesize: '/página',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'Aceptar',\n      cancel: 'Cancelar',\n      error: 'Entrada inválida',\n    },\n    upload: {\n      deleteTip: 'Pulse Eliminar para retirar',\n      delete: 'Eliminar',\n      preview: 'Vista Previa',\n      continue: 'Continuar',\n    },\n    table: {\n      emptyText: 'Sin Datos',\n      confirmFilter: 'Confirmar',\n      resetFilter: 'Reiniciar',\n      clearFilter: 'Despejar',\n      sumText: 'Suma',\n    },\n    tree: {\n      emptyText: 'Sin Datos',\n    },\n    transfer: {\n      noMatch: 'No hay datos que coincidan',\n      noData: 'Sin datos',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Ingresar palabra clave',\n      noCheckedFormat: '{total} artículos',\n      hasCheckedFormat: '{checked}/{total} revisados',\n    },\n    image: {\n      error: 'HA FALLADO',\n    },\n    pageHeader: {\n      title: 'Volver',\n    },\n    popconfirm: {\n      confirmButtonText: 'Si',\n      cancelButtonText: 'No',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,4BAA4B,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}