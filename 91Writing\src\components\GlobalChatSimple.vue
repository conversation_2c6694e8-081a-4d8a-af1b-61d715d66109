<template>
  <div class="global-chat">
    <!-- 聊天按钮 -->
    <div 
      class="chat-toggle-btn"
      @click="toggleChat"
      v-show="!showChat"
    >
      💬 AI助手
    </div>

    <!-- 聊天窗口 -->
    <div 
      class="chat-window"
      v-show="showChat"
    >
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="chat-title">
          🤖 AI写作助手
        </div>
        <div class="chat-actions">
          <button @click="clearChat" title="清空对话">🗑️</button>
          <button @click="toggleChat" title="关闭">✕</button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content" ref="chatContent">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-text">
            <h4>👋 你好！我是AI写作助手</h4>
            <p>我可以帮助你：</p>
            <ul>
              <li>📝 写作建议和灵感</li>
              <li>🔍 情节分析和优化</li>
              <li>✨ 文本润色和改进</li>
              <li>💡 创意头脑风暴</li>
            </ul>
            <p>有什么写作问题都可以问我！</p>
          </div>
        </div>

        <!-- 聊天消息 -->
        <div 
          v-for="(message, index) in messages" 
          :key="index"
          class="message-item"
          :class="message.role"
        >
          <div class="message-avatar">
            {{ message.role === 'user' ? '👤' : '🤖' }}
          </div>
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="message-item assistant">
          <div class="message-avatar">🤖</div>
          <div class="message-content">
            <div class="typing-indicator">正在思考...</div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <textarea
            v-model="inputMessage"
            placeholder="输入你的问题..."
            @keydown.enter.exact="handleSend"
            :disabled="isLoading"
            rows="2"
          ></textarea>
          <button 
            @click="handleSend"
            :disabled="!inputMessage.trim() || isLoading"
            class="send-btn"
          >
            {{ isLoading ? '...' : '➤' }}
          </button>
        </div>
        <div class="input-tips">
          <span>Enter发送，Shift+Enter换行</span>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div 
      v-if="showChat" 
      class="chat-overlay"
      @click="toggleChat"
    ></div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import apiService from '@/services/api'

// 响应式数据
const showChat = ref(false)
const inputMessage = ref('')
const isLoading = ref(false)
const chatContent = ref(null)

// 聊天消息
const messages = reactive([])

// 切换聊天窗口
const toggleChat = () => {
  showChat.value = !showChat.value
  if (showChat.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 清空聊天记录
const clearChat = () => {
  messages.splice(0, messages.length)
  ElMessage.success('聊天记录已清空')
}

// 发送消息
const handleSend = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息
  messages.push({
    role: 'user',
    content: userMessage,
    timestamp: new Date()
  })

  scrollToBottom()

  try {
    isLoading.value = true

    // 调用AI API
    const response = await apiService.generateTextStream(
      `你是一个专业的AI写作助手，请根据用户的问题提供有用的建议和帮助。

用户问题：${userMessage}

请提供专业、有用的回答。如果是写作相关的问题，请给出具体的建议和示例。`,
      {
        maxTokens: 2000,
        temperature: 0.7
      }
    )

    // 添加AI回复
    messages.push({
      role: 'assistant',
      content: response,
      timestamp: new Date()
    })

  } catch (error) {
    console.error('AI回复失败:', error)
    ElMessage.error('AI回复失败，请稍后重试')
    
    // 添加错误消息
    messages.push({
      role: 'assistant',
      content: '抱歉，我暂时无法回复。请检查网络连接或稍后重试。',
      timestamp: new Date()
    })
  } finally {
    isLoading.value = false
    scrollToBottom()
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContent.value) {
      chatContent.value.scrollTop = chatContent.value.scrollHeight
    }
  })
}

// 格式化时间
const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
}
</script>

<style scoped>
.global-chat {
  position: fixed;
  z-index: 9999;
}

/* 聊天按钮 */
.chat-toggle-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;
}

.chat-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

/* 聊天窗口 */
.chat-window {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 380px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 聊天头部 */
.chat-header {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  font-weight: 600;
  font-size: 16px;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.chat-actions button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.chat-actions button:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 聊天内容 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

/* 欢迎消息 */
.welcome-message {
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.welcome-text h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.welcome-text p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.welcome-text ul {
  margin: 12px 0;
  padding-left: 20px;
  color: #606266;
  font-size: 14px;
}

.welcome-text li {
  margin: 6px 0;
  line-height: 1.4;
}

/* 消息项 */
.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
  background: #f0f0f0;
}

.message-content {
  flex: 1;
  max-width: 280px;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  word-wrap: break-word;
}

.user .message-text {
  background: #409eff;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  text-align: right;
}

.user .message-time {
  text-align: left;
}

/* 输入状态 */
.typing-indicator {
  padding: 12px 16px;
  background: white;
  border-radius: 12px;
  color: #909399;
  font-style: italic;
}

/* 输入区域 */
.chat-input {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #ebeef5;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 8px 12px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  outline: none;
}

.input-container textarea:focus {
  border-color: #409eff;
}

.send-btn {
  height: 40px;
  width: 40px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #337ecc;
}

.send-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

/* 遮罩层 */
.chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-window {
    width: calc(100vw - 40px);
    height: calc(100vh - 60px);
    bottom: 20px;
    right: 20px;
  }
  
  .chat-toggle-btn {
    bottom: 20px;
    right: 20px;
  }
}

/* 滚动条样式 */
.chat-content::-webkit-scrollbar {
  width: 6px;
}

.chat-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  background: #909399;
}
</style>
