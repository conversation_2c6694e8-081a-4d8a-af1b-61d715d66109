<template>
  <div class="global-chat">
    <!-- 聊天按钮 -->
    <div 
      class="chat-toggle-btn"
      @click="toggleChat"
      v-show="!showChat"
    >
      💬 AI助手
    </div>

    <!-- 聊天窗口 -->
    <div 
      class="chat-window"
      v-show="showChat"
    >
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="chat-title">
          🤖 AI写作助手
        </div>
        <div class="chat-actions">
          <button @click="clearChat" title="清空对话">🗑️</button>
          <button @click="toggleChat" title="关闭">✕</button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content" ref="chatContent">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-text">
            <h4>👋 你好！我是AI写作助手</h4>
            <p>我可以帮助你：</p>
            <ul>
              <li>📝 写作建议和灵感</li>
              <li>🔍 情节分析和优化</li>
              <li>✨ 文本润色和改进</li>
              <li>💡 创意头脑风暴</li>
            </ul>
            <p>有什么写作问题都可以问我！</p>
          </div>
        </div>

        <!-- 聊天消息 -->
        <div 
          v-for="(message, index) in messages" 
          :key="index"
          class="message-item"
          :class="message.role"
        >
          <div class="message-avatar">
            {{ message.role === 'user' ? '👤' : '🤖' }}
          </div>
          <div class="message-content">
            <div class="message-text">{{ message.content }}</div>
            <div class="message-actions" v-if="message.role === 'assistant'">
              <div class="fill-dropdown" @click.stop>
                <button
                  @click="showFillOptions(message.content, $event)"
                  class="fill-btn"
                  title="智能填充"
                >
                  📝 填充 ▼
                </button>
                <div
                  v-if="showFillMenu && currentFillContent === message.content"
                  class="fill-menu"
                >
                  <div @click="fillToEditor(message.content)" class="fill-option">
                    📄 当前编辑器
                  </div>
                  <div @click="fillToWriterEditor(message.content)" class="fill-option">
                    ✍️ 章节内容
                  </div>
                  <div @click="fillToCharacterEditor(message.content)" class="fill-option">
                    👤 人物设定
                  </div>
                  <div @click="fillToWorldEditor(message.content)" class="fill-option">
                    🌍 世界观设定
                  </div>
                  <div @click="appendToEditor(message.content)" class="fill-option">
                    ➕ 追加内容
                  </div>
                  <div @click="replaceSelection(message.content)" class="fill-option">
                    🔄 替换选中
                  </div>
                </div>
              </div>
              <button
                @click="copyMessage(message.content)"
                class="copy-btn"
                title="复制内容"
              >
                📋 复制
              </button>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="message-item assistant">
          <div class="message-avatar">🤖</div>
          <div class="message-content">
            <div class="typing-indicator">正在思考...</div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <textarea
            v-model="inputMessage"
            placeholder="输入你的问题..."
            @keydown.enter.exact="handleSend"
            :disabled="isLoading"
            rows="2"
          ></textarea>
          <button 
            @click="handleSend"
            :disabled="!inputMessage.trim() || isLoading"
            class="send-btn"
          >
            {{ isLoading ? '...' : '➤' }}
          </button>
        </div>
        <div class="input-tips">
          <span>Enter发送，Shift+Enter换行</span>
        </div>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div 
      v-if="showChat" 
      class="chat-overlay"
      @click="toggleChat"
    ></div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import apiService from '@/services/api'

// 响应式数据
const showChat = ref(false)
const inputMessage = ref('')
const isLoading = ref(false)
const chatContent = ref(null)

// 填充菜单相关
const showFillMenu = ref(false)
const currentFillContent = ref('')

// 聊天消息
const messages = reactive([])

// 切换聊天窗口
const toggleChat = () => {
  showChat.value = !showChat.value
  if (showChat.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 清空聊天记录
const clearChat = () => {
  messages.splice(0, messages.length)
  ElMessage.success('聊天记录已清空')
}

// 发送消息
const handleSend = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return

  const userMessage = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息
  messages.push({
    role: 'user',
    content: userMessage,
    timestamp: new Date()
  })

  scrollToBottom()

  try {
    isLoading.value = true

    // 调用AI API
    const response = await apiService.generateTextStream(
      `你是一个专业的AI写作助手，请根据用户的问题提供有用的建议和帮助。

用户问题：${userMessage}

请提供专业、有用的回答。如果是写作相关的问题，请给出具体的建议和示例。`,
      {
        maxTokens: 2000,
        temperature: 0.7
      }
    )

    // 添加AI回复
    messages.push({
      role: 'assistant',
      content: response,
      timestamp: new Date()
    })

  } catch (error) {
    console.error('AI回复失败:', error)
    ElMessage.error('AI回复失败，请稍后重试')
    
    // 添加错误消息
    messages.push({
      role: 'assistant',
      content: '抱歉，我暂时无法回复。请检查网络连接或稍后重试。',
      timestamp: new Date()
    })
  } finally {
    isLoading.value = false
    scrollToBottom()
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContent.value) {
      chatContent.value.scrollTop = chatContent.value.scrollHeight
    }
  })
}

// 格式化时间
const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
}

// 显示填充选项菜单
const showFillOptions = (content, event) => {
  currentFillContent.value = content
  showFillMenu.value = !showFillMenu.value

  // 点击其他地方关闭菜单
  if (showFillMenu.value) {
    setTimeout(() => {
      document.addEventListener('click', hideFillMenu, { once: true })
    }, 100)
  }
}

// 隐藏填充菜单
const hideFillMenu = () => {
  showFillMenu.value = false
  currentFillContent.value = ''
}

// 复制消息内容
const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('内容已复制到剪贴板')
    hideFillMenu()
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 智能填充到编辑器
const fillToEditor = (content) => {
  hideFillMenu()

  try {
    // 获取当前路由信息
    const currentRoute = window.location.hash || window.location.pathname

    // 根据当前页面智能填充
    if (currentRoute.includes('writer') || currentRoute.includes('Writer')) {
      fillToWriterEditor(content)
    } else if (currentRoute.includes('character') || currentRoute.includes('人物')) {
      fillToCharacterEditor(content)
    } else if (currentRoute.includes('world') || currentRoute.includes('世界观')) {
      fillToWorldEditor(content)
    } else {
      // 默认尝试填充到任何可见的编辑器
      fillToAnyEditor(content)
    }
  } catch (error) {
    console.error('填充失败:', error)
    ElMessage.error('填充失败，请手动复制粘贴')
  }
}

// 填充到写作编辑器
const fillToWriterEditor = (content) => {
  hideFillMenu()

  // 尝试找到富文本编辑器
  const editors = [
    // Quill编辑器
    document.querySelector('.ql-editor'),
    // TinyMCE编辑器
    document.querySelector('.mce-content-body'),
    // 普通textarea
    document.querySelector('textarea[placeholder*="章节内容"]'),
    document.querySelector('textarea[placeholder*="正文"]'),
    document.querySelector('textarea[placeholder*="内容"]'),
    // 任何大的textarea
    ...Array.from(document.querySelectorAll('textarea')).filter(el =>
      el.offsetHeight > 100 && el.offsetWidth > 200
    )
  ].filter(Boolean)

  if (editors.length > 0) {
    const editor = editors[0]

    if (editor.classList.contains('ql-editor')) {
      // Quill编辑器
      editor.innerHTML = `<p>${content.replace(/\n/g, '</p><p>')}</p>`
      editor.dispatchEvent(new Event('input', { bubbles: true }))
    } else {
      // 普通textarea - 替换全部内容
      editor.value = content
      editor.dispatchEvent(new Event('input', { bubbles: true }))
      editor.dispatchEvent(new Event('change', { bubbles: true }))
    }

    ElMessage.success('内容已填充到章节编辑器')

    // 滚动到编辑器位置
    editor.scrollIntoView({ behavior: 'smooth', block: 'center' })

    // 聚焦编辑器
    setTimeout(() => {
      editor.focus()
    }, 500)
  } else {
    ElMessage.warning('未找到章节编辑器，内容已复制到剪贴板')
    copyMessage(content)
  }
}

// 追加内容到编辑器
const appendToEditor = (content) => {
  hideFillMenu()

  const editors = [
    document.querySelector('.ql-editor'),
    document.querySelector('.mce-content-body'),
    ...Array.from(document.querySelectorAll('textarea')).filter(el =>
      el.offsetHeight > 50 && el.offsetWidth > 100
    )
  ].filter(Boolean)

  if (editors.length > 0) {
    const editor = editors[0]

    if (editor.classList.contains('ql-editor')) {
      // Quill编辑器
      editor.innerHTML += `<p>${content.replace(/\n/g, '</p><p>')}</p>`
      editor.dispatchEvent(new Event('input', { bubbles: true }))
    } else {
      // 普通textarea
      const currentValue = editor.value
      const newValue = currentValue ? `${currentValue}\n\n${content}` : content
      editor.value = newValue
      editor.dispatchEvent(new Event('input', { bubbles: true }))
      editor.dispatchEvent(new Event('change', { bubbles: true }))
    }

    ElMessage.success('内容已追加到编辑器')
    editor.scrollIntoView({ behavior: 'smooth', block: 'center' })
    setTimeout(() => editor.focus(), 500)
  } else {
    ElMessage.warning('未找到编辑器，内容已复制到剪贴板')
    copyMessage(content)
  }
}

// 替换选中内容
const replaceSelection = (content) => {
  hideFillMenu()

  const activeElement = document.activeElement

  if (activeElement && (activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'INPUT')) {
    const start = activeElement.selectionStart
    const end = activeElement.selectionEnd
    const currentValue = activeElement.value

    if (start !== end) {
      // 有选中内容，替换选中部分
      const newValue = currentValue.substring(0, start) + content + currentValue.substring(end)
      activeElement.value = newValue
      activeElement.dispatchEvent(new Event('input', { bubbles: true }))
      activeElement.dispatchEvent(new Event('change', { bubbles: true }))

      // 设置光标位置到插入内容的末尾
      const newCursorPos = start + content.length
      activeElement.setSelectionRange(newCursorPos, newCursorPos)

      ElMessage.success('已替换选中内容')
    } else {
      // 没有选中内容，在光标位置插入
      const newValue = currentValue.substring(0, start) + content + currentValue.substring(start)
      activeElement.value = newValue
      activeElement.dispatchEvent(new Event('input', { bubbles: true }))
      activeElement.dispatchEvent(new Event('change', { bubbles: true }))

      // 设置光标位置到插入内容的末尾
      const newCursorPos = start + content.length
      activeElement.setSelectionRange(newCursorPos, newCursorPos)

      ElMessage.success('内容已插入到光标位置')
    }
  } else {
    ElMessage.warning('请先选中要替换的文本或点击编辑器')
    copyMessage(content)
  }
}

// 填充到人物编辑器
const fillToCharacterEditor = (content) => {
  hideFillMenu()

  const characterFields = [
    document.querySelector('textarea[placeholder*="人物描述"]'),
    document.querySelector('textarea[placeholder*="性格"]'),
    document.querySelector('textarea[placeholder*="背景"]'),
    document.querySelector('input[placeholder*="人物名称"]'),
    document.querySelector('textarea[placeholder*="简介"]')
  ].filter(Boolean)

  if (characterFields.length > 0) {
    const field = characterFields[0]
    field.value = content
    field.dispatchEvent(new Event('input', { bubbles: true }))
    field.dispatchEvent(new Event('change', { bubbles: true }))
    ElMessage.success('内容已填充到人物设定')
    field.scrollIntoView({ behavior: 'smooth', block: 'center' })
    field.focus()
  } else {
    fillToAnyEditor(content)
  }
}

// 填充到世界观编辑器
const fillToWorldEditor = (content) => {
  hideFillMenu()

  const worldFields = [
    document.querySelector('textarea[placeholder*="世界观"]'),
    document.querySelector('textarea[placeholder*="设定"]'),
    document.querySelector('textarea[placeholder*="背景"]'),
    document.querySelector('textarea[placeholder*="描述"]')
  ].filter(Boolean)

  if (worldFields.length > 0) {
    const field = worldFields[0]
    field.value = content
    field.dispatchEvent(new Event('input', { bubbles: true }))
    field.dispatchEvent(new Event('change', { bubbles: true }))
    ElMessage.success('内容已填充到世界观设定')
    field.scrollIntoView({ behavior: 'smooth', block: 'center' })
    field.focus()
  } else {
    fillToAnyEditor(content)
  }
}

// 填充到任何可用的编辑器
const fillToAnyEditor = (content) => {
  // 查找所有可能的编辑器元素
  const allEditors = [
    ...document.querySelectorAll('textarea'),
    ...document.querySelectorAll('.ql-editor'),
    ...document.querySelectorAll('.mce-content-body'),
    ...document.querySelectorAll('[contenteditable="true"]')
  ].filter(el => {
    // 过滤掉隐藏的或太小的元素
    const rect = el.getBoundingClientRect()
    return rect.width > 100 && rect.height > 50 &&
           getComputedStyle(el).display !== 'none' &&
           getComputedStyle(el).visibility !== 'hidden'
  })

  if (allEditors.length > 0) {
    // 选择最大的编辑器
    const editor = allEditors.reduce((largest, current) => {
      const largestRect = largest.getBoundingClientRect()
      const currentRect = current.getBoundingClientRect()
      return (currentRect.width * currentRect.height) > (largestRect.width * largestRect.height)
        ? current : largest
    })

    if (editor.tagName === 'TEXTAREA' || editor.tagName === 'INPUT') {
      const currentValue = editor.value
      const newValue = currentValue ? `${currentValue}\n\n${content}` : content
      editor.value = newValue
      editor.dispatchEvent(new Event('input', { bubbles: true }))
      editor.dispatchEvent(new Event('change', { bubbles: true }))
    } else {
      // 富文本编辑器
      const currentContent = editor.innerHTML
      editor.innerHTML = currentContent + `<p>${content.replace(/\n/g, '</p><p>')}</p>`
      editor.dispatchEvent(new Event('input', { bubbles: true }))
    }

    ElMessage.success('内容已填充到编辑器')
    editor.scrollIntoView({ behavior: 'smooth', block: 'center' })
    setTimeout(() => editor.focus(), 500)
  } else {
    ElMessage.warning('未找到可用的编辑器，内容已复制到剪贴板')
    copyMessage(content)
  }
}
</script>

<style scoped>
.global-chat {
  position: fixed;
  z-index: 9999;
}

/* 聊天按钮 */
.chat-toggle-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;
}

.chat-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

/* 聊天窗口 */
.chat-window {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 380px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 聊天头部 */
.chat-header {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  font-weight: 600;
  font-size: 16px;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.chat-actions button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.chat-actions button:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 聊天内容 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

/* 欢迎消息 */
.welcome-message {
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.welcome-text h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.welcome-text p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.welcome-text ul {
  margin: 12px 0;
  padding-left: 20px;
  color: #606266;
  font-size: 14px;
}

.welcome-text li {
  margin: 6px 0;
  line-height: 1.4;
}

/* 消息项 */
.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
  background: #f0f0f0;
}

.message-content {
  flex: 1;
  max-width: 280px;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  word-wrap: break-word;
}

.user .message-text {
  background: #409eff;
  color: white;
}

.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.fill-dropdown {
  position: relative;
  display: inline-block;
}

.fill-btn, .copy-btn {
  background: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
}

.fill-btn:hover {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.copy-btn:hover {
  background: #67c23a;
  color: white;
  border-color: #67c23a;
}

.fill-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 140px;
  margin-top: 4px;
}

.fill-option {
  padding: 8px 12px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  transition: background 0.2s;
  border-bottom: 1px solid #f5f7fa;
}

.fill-option:last-child {
  border-bottom: none;
}

.fill-option:hover {
  background: #f5f7fa;
  color: #409eff;
}

.fill-option:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.fill-option:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  text-align: right;
}

.user .message-time {
  text-align: left;
}

/* 输入状态 */
.typing-indicator {
  padding: 12px 16px;
  background: white;
  border-radius: 12px;
  color: #909399;
  font-style: italic;
}

/* 输入区域 */
.chat-input {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #ebeef5;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 8px 12px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  outline: none;
}

.input-container textarea:focus {
  border-color: #409eff;
}

.send-btn {
  height: 40px;
  width: 40px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #337ecc;
}

.send-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

/* 遮罩层 */
.chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-window {
    width: calc(100vw - 40px);
    height: calc(100vh - 60px);
    bottom: 20px;
    right: 20px;
  }
  
  .chat-toggle-btn {
    bottom: 20px;
    right: 20px;
  }
}

/* 滚动条样式 */
.chat-content::-webkit-scrollbar {
  width: 6px;
}

.chat-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  background: #909399;
}
</style>
