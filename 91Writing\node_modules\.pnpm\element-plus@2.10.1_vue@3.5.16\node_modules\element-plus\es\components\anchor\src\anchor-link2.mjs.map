{"version": 3, "file": "anchor-link2.mjs", "sources": ["../../../../../../packages/components/anchor/src/anchor-link.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.e('item')\">\n    <a ref=\"linkRef\" :class=\"cls\" :href=\"href\" @click=\"handleClick\">\n      <slot>{{ title }}</slot>\n    </a>\n    <div\n      v-if=\"$slots['sub-link'] && direction === 'vertical'\"\n      :class=\"ns.e('list')\"\n    >\n      <slot name=\"sub-link\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { anchorLinkProps } from './anchor-link'\nimport { anchorKey } from './constants'\n\ndefineOptions({\n  name: 'ElAnchorLink',\n})\n\nconst props = defineProps(anchorLinkProps)\n\nconst linkRef = ref<HTMLElement | null>(null)\n\nconst {\n  ns,\n  direction,\n  currentAnchor,\n  addLink,\n  removeLink,\n  handleClick: contextHandleClick,\n} = inject(anchorKey)!\n\nconst cls = computed(() => [\n  ns.e('link'),\n  ns.is('active', currentAnchor.value === props.href),\n])\n\nconst handleClick = (e: MouseEvent) => {\n  contextHandleClick(e, props.href)\n}\n\nwatch(\n  () => props.href,\n  (val, oldVal) => {\n    nextTick(() => {\n      if (oldVal) removeLink(oldVal)\n      if (val) {\n        addLink({\n          href: val,\n          el: linkRef.value!,\n        })\n      }\n    })\n  }\n)\n\nonMounted(() => {\n  const { href } = props\n  if (href) {\n    addLink({\n      href,\n      el: linkRef.value!,\n    })\n  }\n})\n\nonBeforeUnmount(() => {\n  const { href } = props\n  if (href) {\n    removeLink(href)\n  }\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock"], "mappings": ";;;;;mCA2Bc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,OAAA,GAAU,IAAwB,IAAI,CAAA,CAAA;AAE5C,IAAM,MAAA;AAAA,MACJ,EAAA;AAAA,MACA,SAAA;AAAA,MACA,aAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAa,EAAA,kBAAA;AAAA,KACf,GAAI,OAAO,SAAS,CAAA,CAAA;AAEpB,IAAM,MAAA,GAAA,GAAM,SAAS,MAAM;AAAA,MACzB,EAAA,CAAG,EAAE,MAAM,CAAA;AAAA,MACX,GAAG,EAAG,CAAA,QAAA,EAAU,aAAc,CAAA,KAAA,KAAU,MAAM,IAAI,CAAA;AAAA,KACnD,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,CAAC,CAAkB,KAAA;AACrC,MAAmB,kBAAA,CAAA,CAAA,EAAG,MAAM,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA;AAEA,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,IAAA,EAAA,CAAA,GAAA,EAAA,MAAA,KAAA;AAAA,MACE,QAAY,CAAA,MAAA;AAAA,YACN,MAAW;AACf,UAAA,UAAe,CAAA,MAAA,CAAA,CAAA;AACb,QAAI,IAAA,GAAA,EAAA;AACJ,UAAA,OAAS,CAAA;AACP,YAAQ,IAAA,EAAA,GAAA;AAAA,YAAA,EACN,EAAM,OAAA,CAAA,KAAA;AAAA,WAAA,CAAA,CAAA;AACM,SAAA;AACb,OACH,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACD,IACH,SAAA,CAAA,MAAA;AAAA,MACF,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAA,IAAU,EAAM;AACd,QAAM;AACN,UAAI,IAAM;AACR,UAAQ,EAAA,EAAA,OAAA,CAAA,KAAA;AAAA,SACN,CAAA,CAAA;AAAA,OAAA;AACY,KAAA,CAAA,CAAA;AACb,IACH,eAAA,CAAA,MAAA;AAAA,MACD,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAED,MAAA,IAAA,IAAA,EAAA;AACE,QAAM,WAAO,IAAI,CAAA,CAAA;AACjB,OAAA;AACE,KAAA,CAAA,CAAA;AAAe,IACjB,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}