# 聊天助手功能说明

## 功能概述
为每个管理对话框添加了AI聊天助手功能，用户可以在管理内容时直接与AI对话获得创作建议和帮助。

## 聊天助手类型

### 👥 人物创作助手
**位置**: 人物管理对话框右侧
**功能**: 
- 角色创建建议
- 性格设定指导
- 背景故事构思
- 角色关系建议

**快捷提示词**:
- "帮我创建一个主角角色"
- "帮我完善这个角色的背景故事"
- "给我一些角色性格建议"

### 🌍 世界观创作助手
**位置**: 世界观管理对话框右侧
**功能**:
- 世界观设定建议
- 魔法体系设计
- 政治势力构建
- 地理环境规划

**快捷提示词**:
- "帮我设计一个魔法体系"
- "创建一个政治势力"
- "设计地理环境"

### 📚 语料创作助手
**位置**: 语料库管理对话框右侧
**功能**:
- 场景描述指导
- 对话模板创建
- 情感表达建议
- 写作技巧分享

**快捷提示词**:
- "帮我写一段场景描述"
- "创建对话模板"
- "情感表达语料"

### 📊 事件线助手
**位置**: 事件线管理对话框右侧
**功能**:
- 剧情发展规划
- 关键事件设计
- 时间线安排
- 情节逻辑检查

**快捷提示词**:
- "帮我规划剧情发展"
- "创建关键事件"
- "时间线建议"

## 界面设计

### 聊天区域布局
```
┌─────────────────────────────────────┐
│ 🤖 [助手名称]        [清空对话]     │
├─────────────────────────────────────┤
│                                     │
│  👤 用户消息                        │
│      └─ 时间戳                      │
│                                     │
│              🤖 AI回复              │
│                  └─ 时间戳          │
│                                     │
├─────────────────────────────────────┤
│ [快捷按钮1] [快捷按钮2] [快捷按钮3] │
├─────────────────────────────────────┤
│ ┌─────────────────────┐ [发送]      │
│ │ 输入框...           │             │
│ │                     │             │
│ └─────────────────────┘             │
└─────────────────────────────────────┘
```

### 视觉特点
- **宽度**: 400px固定宽度
- **高度**: 跟随对话框高度
- **分割**: 左侧内容区域，右侧聊天区域
- **背景**: 浅灰色背景区分
- **动画**: 消息淡入动画，打字指示器

## 交互功能

### 开启/关闭聊天
- 点击"AI助手"按钮切换聊天区域显示
- 按钮状态变化：普通 → 警告色（表示已开启）
- 对话框宽度自动调整：1000px → 1200px

### 消息发送
- **方式1**: 点击发送按钮
- **方式2**: Ctrl+Enter快捷键
- **限制**: 输入内容不能为空

### 快捷提示词
- 预设常用问题按钮
- 点击自动填入输入框并发送
- 针对不同助手类型定制

### 消息管理
- 自动滚动到最新消息
- 清空对话历史功能
- 消息时间戳显示

## 技术实现

### 状态管理
```javascript
// 聊天显示状态
const showCharacterChat = ref(false)
const showWorldviewChat = ref(false)
const showCorpusChat = ref(false)
const showEventsChat = ref(false)

// 聊天历史
const characterChatHistory = ref([])
const worldviewChatHistory = ref([])
const corpusChatHistory = ref([])
const eventsChatHistory = ref([])

// 输入内容
const characterChatInput = ref('')
const worldviewChatInput = ref('')
const corpusChatInput = ref('')
const eventsChatInput = ref('')

// 加载状态
const isCharacterChatLoading = ref(false)
const isWorldviewChatLoading = ref(false)
const isCorpusChatLoading = ref(false)
const isEventsChatLoading = ref(false)
```

### 消息格式
```javascript
{
  role: 'user' | 'assistant',
  content: '消息内容',
  timestamp: 1234567890
}
```

### AI回复模拟
- 随机延迟1-3秒模拟真实响应
- 根据助手类型返回不同建议
- 错误处理和用户提示

## 样式特色

### 消息气泡
- **用户消息**: 蓝色背景，右对齐
- **AI消息**: 白色背景，左对齐
- **圆角设计**: 12px圆角
- **阴影效果**: 轻微阴影

### 打字指示器
- 三个跳动的圆点
- 灰色背景
- 动画效果

### 响应式设计
- 消息自适应宽度
- 长文本自动换行
- 滚动条样式优化

## 使用场景

### 创作灵感
- 遇到创作瓶颈时寻求建议
- 快速获得创作思路
- 专业写作技巧指导

### 内容完善
- 现有内容的改进建议
- 逻辑性检查
- 细节补充建议

### 学习交流
- 写作技巧学习
- 创作经验分享
- 问题解答

## 扩展计划

### 功能增强
1. **上下文感知**: AI能理解当前编辑的内容
2. **个性化建议**: 根据用户写作风格调整建议
3. **历史记录**: 保存聊天历史到本地
4. **导出功能**: 将有用的建议导出为笔记

### 集成优化
1. **一键应用**: 将AI建议直接应用到内容中
2. **模板生成**: 根据对话生成内容模板
3. **智能推荐**: 主动推荐相关功能和内容

### 体验优化
1. **语音输入**: 支持语音转文字输入
2. **快捷键**: 更多键盘快捷操作
3. **主题定制**: 聊天界面主题选择

## 测试建议

### 功能测试
1. 测试所有助手的开启/关闭
2. 验证消息发送和接收
3. 检查快捷提示词功能
4. 测试清空对话功能

### 界面测试
1. 检查不同屏幕尺寸下的显示
2. 验证动画效果
3. 测试长消息的显示
4. 检查滚动行为

### 交互测试
1. 测试键盘快捷键
2. 验证加载状态显示
3. 测试错误处理
4. 检查响应时间

这个聊天助手功能大大增强了用户的创作体验，提供了即时的AI支持和建议！
