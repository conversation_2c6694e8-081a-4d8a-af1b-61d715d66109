# 主编辑器智能创作助手功能说明

## 功能概述
在主编辑器页面右侧新增了智能创作助手面板，可以通过自然语言对话来控制和管理整个小说的内容，包括角色、世界观、剧情、事件等，并且能够直接添加到相应的管理模块中。

## 功能特点

### 🎯 智能意图识别
AI助手能够自动识别用户的创作意图：
- **角色创建**: 识别"角色"、"人物"等关键词
- **世界观设定**: 识别"世界观"、"设定"等关键词  
- **事件规划**: 识别"事件"、"剧情"等关键词
- **内容续写**: 识别"续写"、"写作"等关键词

### 🤖 一站式创作管理
- **角色管理**: 生成完整角色信息并直接添加到角色库
- **世界观管理**: 创建世界观设定并添加到设定库
- **事件管理**: 规划关键事件并添加到事件线
- **内容创作**: 生成剧情内容并应用到当前章节

### 🎮 便捷的交互体验
- **侧边面板**: 固定在编辑器右侧，不影响写作流程
- **快捷按钮**: 提供常用创作功能的快速入口
- **一键应用**: 生成的内容可直接添加到相应模块
- **实时对话**: 支持连续对话和上下文理解

## 界面设计

### 助手面板布局
```
┌─────────────────────────────────┐
│ 🤖 智能创作助手        清空对话 │
├─────────────────────────────────┤
│                                 │
│        对话消息区域              │
│                                 │
├─────────────────────────────────┤
│ [创建角色] [设计世界观] [规划剧情] │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ 输入框                      │ │
│ └─────────────────────────────┘ │
│                        [发送]   │
└─────────────────────────────────┘
```

### 智能操作按钮
根据AI回复的内容类型，自动显示相应的操作按钮：
- **添加角色**: 将生成的角色信息添加到角色管理
- **添加世界观**: 将世界观设定添加到世界观管理
- **添加事件**: 将事件信息添加到事件线管理
- **应用到编辑器**: 将剧情内容插入到当前章节

## 技术实现

### 1. 意图识别算法
```javascript
const mockMainAssistantResponse = async (input) => {
  // 检测用户意图
  if (input.includes('角色') || input.includes('人物')) {
    return generateCharacterResponse(input)
  } else if (input.includes('世界观') || input.includes('设定')) {
    return generateWorldviewResponse(input)
  } else if (input.includes('事件') || input.includes('剧情')) {
    return generateEventResponse(input)
  } else if (input.includes('续写') || input.includes('写作')) {
    return generatePlotResponse(input)
  }
}
```

### 2. 结构化数据生成
每种类型的回复都包含结构化的数据：
```javascript
{
  content: '文本回复内容',
  actionData: {
    type: 'character', // 类型：character/worldview/event/plot
    data: {
      // 具体的数据结构
      name: '角色名',
      age: 25,
      // ... 其他属性
    }
  }
}
```

### 3. 一键添加功能
```javascript
const addCharacterFromChat = (characterData) => {
  const newCharacter = {
    id: Date.now().toString(),
    ...characterData,
    createdAt: new Date().toISOString()
  }
  characters.value.push(newCharacter)
  ElMessage.success(`角色已添加`)
  saveNovelData()
}
```

## 使用场景

### 1. 角色创建
**用户输入**: "帮我创建一个神秘的法师角色"
**AI回复**: 
```
我为你设计了一个新角色：

**林晨**
- 年龄：28岁
- 性格：神秘而富有魅力，拥有独特的人生经历
- 背景：林晨是一个来自远方的旅者，身上隐藏着许多秘密
- 动机：寻找内心的真正归属

点击下方按钮可以直接添加到角色管理中！
```
**操作**: 点击"添加角色"按钮，角色自动添加到角色库

### 2. 世界观设定
**用户输入**: "设计一个魔法世界的背景"
**AI回复**:
```
我为你设计了一个世界观设定：

**魔法世界**
这是一个充满奇幻色彩的魔法世界，拥有独特的力量体系和文化背景。

在这个世界中，存在着古老的传说和神秘的力量...

点击下方按钮可以添加到世界观管理中！
```
**操作**: 点击"添加世界观"按钮，设定自动添加到世界观库

### 3. 剧情规划
**用户输入**: "规划一个转折点剧情"
**AI回复**:
```
我为你设计了一个关键事件：

**重要转折**
这是故事中的一个重要重要转折，将推动剧情向前发展。

建议安排在：第一章

点击下方按钮可以添加到事件线中！
```
**操作**: 点击"添加事件"按钮，事件自动添加到事件线

### 4. 内容续写
**用户输入**: "帮我续写当前章节的剧情"
**AI回复**:
```
基于你的需求，我建议这样发展剧情：

主角在这个关键时刻面临重要选择，这个决定将影响整个故事的走向。通过内心的挣扎和外在的冲突，展现角色的成长和变化。

建议加入一些细节描写来增强代入感，比如环境描写、心理活动等。
```
**操作**: 点击"应用到编辑器"按钮，内容自动插入到当前章节

## 快捷功能

### 快速提示按钮
- **创建角色**: 快速生成角色创建提示
- **设计世界观**: 快速生成世界观设计提示
- **规划剧情**: 快速生成剧情规划提示
- **创建事件**: 快速生成事件创建提示

### 键盘快捷键
- **Ctrl + Enter**: 发送消息
- **创作助手按钮**: 快速开启/关闭助手面板

## 响应式设计

### 面板布局
- **固定宽度**: 助手面板宽度400px
- **自适应高度**: 根据浏览器窗口高度调整
- **弹性布局**: 编辑器区域自动调整宽度

### 移动端适配
- 小屏幕设备上助手面板可收起
- 保持核心功能的可用性
- 优化触摸操作体验

## 数据流程

### 1. 用户输入 → AI分析
```
用户输入 → 意图识别 → 选择生成器 → 生成回复和数据
```

### 2. AI回复 → 用户操作
```
显示回复 → 显示操作按钮 → 用户点击 → 执行相应操作
```

### 3. 数据同步 → 持久化
```
添加到内存 → 更新界面 → 保存到本地存储
```

## 扩展功能

### 1. 上下文记忆
- 记住之前的对话内容
- 基于历史对话提供更精准的建议
- 支持多轮对话的连续性

### 2. 个性化学习
- 学习用户的创作偏好
- 记住常用的角色类型和设定
- 提供个性化的创作建议

### 3. 批量操作
- 一次生成多个角色
- 批量创建事件线
- 整体世界观规划

### 4. 智能关联
- 自动关联相关角色和事件
- 检测设定冲突
- 提供一致性建议

## 使用建议

### 1. 最佳实践
- 使用具体的描述词汇
- 提供足够的上下文信息
- 及时保存生成的内容

### 2. 创作流程
1. 先用助手规划整体框架
2. 逐步完善各个模块内容
3. 在写作过程中随时咨询助手
4. 利用助手优化和完善内容

### 3. 效率提升
- 善用快捷按钮
- 批量处理相似内容
- 建立个人的创作模板

这个智能创作助手真正实现了AI与创作流程的深度整合，让小说创作变得更加高效和有趣！
