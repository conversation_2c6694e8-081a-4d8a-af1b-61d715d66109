{"version": 3, "sources": ["../../.pnpm/element-plus@2.10.1_vue@3.5.16/node_modules/element-plus/es/components/select/style/css.mjs"], "sourcesContent": ["import '../../base/style/css.mjs';\nimport '../../tag/style/css.mjs';\nimport '../../option/style/css.mjs';\nimport '../../option-group/style/css.mjs';\nimport '../../scrollbar/style/css.mjs';\nimport '../../popper/style/css.mjs';\nimport 'element-plus/theme-chalk/el-select.css';\n//# sourceMappingURL=css.mjs.map\n"], "mappings": ";AAMA,OAAO;", "names": []}