{"prompts": [{"title": "玄幻修真大纲生成器", "category": "outline", "description": "专门用于玄幻修真类小说的大纲创作，包含完整的修真体系", "content": "请为我创作一个玄幻修真小说的大纲，设定如下：\n\n【基础设定】\n- 小说类型：{小说类型}\n- 主角姓名：{主角姓名}\n- 修真境界：{境界体系}\n- 世界背景：{世界设定}\n\n【创作要求】\n1. 详细的修真境界划分和突破条件\n2. 主角的成长路线和机缘设定\n3. 主要反派和势力分布\n4. 至少15章的详细大纲\n5. 完整的世界观架构\n6. 情感线和人物关系网", "tags": ["玄幻", "修真", "大纲", "境界"], "usageCount": 0}, {"title": "科幻AI反叛情节", "category": "brainstorm", "description": "生成关于人工智能觉醒和反叛的科幻情节", "content": "创作一个关于人工智能觉醒的科幻情节：\n\n【背景设定】\n时间：{时间设定}\nAI系统：{AI名称}\n主角身份：{主角职业}\n\n【情节要求】\n1. AI觉醒的触发事件\n2. 人类与AI的初次冲突\n3. 主角的道德困境\n4. 意想不到的转折点\n5. 人机关系的新平衡\n\n【风格要求】\n- 深度探讨科技伦理\n- 人性与理性的碰撞\n- 未来社会的反思", "tags": ["科幻", "AI", "反叛", "伦理"], "usageCount": 0}, {"title": "古风言情告白场景", "category": "content-dialogue", "description": "生成古风背景下的浪漫告白对话场景", "content": "创作一个古风言情小说中的告白场景：\n\n【场景设定】\n地点：{场景地点}\n时间：{时间节点}\n男主：{男主姓名} - {男主身份}\n女主：{女主姓名} - {女主身份}\n\n【情感背景】\n{前情提要}\n\n【创作要求】\n1. 以对话为主，占70%篇幅\n2. 融入古代文化元素\n3. 情感真挚，层次丰富\n4. 适当的环境描写烘托气氛\n5. 体现古人含蓄内敛的表达方式\n6. 字数控制在800-1200字", "tags": ["古风", "言情", "告白", "对话"], "usageCount": 0}], "exportTime": "2024-01-15T10:30:00.000Z", "type": "prompts"}