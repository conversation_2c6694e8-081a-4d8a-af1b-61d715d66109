{"version": 3, "file": "pl.min.mjs", "sources": ["../../../../packages/locale/lang/pl.ts"], "sourcesContent": ["export default {\n  name: 'pl',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON><PERSON><PERSON>',\n      cancel: 'Anuluj',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON><PERSON> datę',\n      selectTime: '<PERSON><PERSON><PERSON><PERSON> godzin<PERSON>',\n      startDate: 'Data początkowa',\n      startTime: '<PERSON><PERSON><PERSON> początkowa',\n      endDate: 'Data końcowa',\n      endTime: '<PERSON><PERSON> końcowa',\n      prevYear: 'Poprzedni rok',\n      nextYear: 'Następny rok',\n      prevMonth: 'Poprzedni miesiąc',\n      nextMonth: 'Następny miesiąc',\n      year: 'rok',\n      month1: 'styczeń',\n      month2: 'luty',\n      month3: 'marzec',\n      month4: 'kwiecie<PERSON>',\n      month5: 'maj',\n      month6: 'czerwiec',\n      month7: 'lipiec',\n      month8: 'sierpie<PERSON>',\n      month9: 'wrzesie<PERSON>',\n      month10: 'paźd<PERSON><PERSON>',\n      month11: 'listopad',\n      month12: 'grudzie<PERSON>',\n      week: 'tydzień',\n      weeks: {\n        sun: 'niedz.',\n        mon: 'pon.',\n        tue: 'wt.',\n        wed: 'śr.',\n        thu: 'czw.',\n        fri: 'pt.',\n        sat: 'sob.',\n      },\n      months: {\n        jan: 'STY',\n        feb: 'LUT',\n        mar: 'MAR',\n        apr: 'KWI',\n        may: 'MAJ',\n        jun: 'CZE',\n        jul: 'LIP',\n        aug: 'SIE',\n        sep: 'WRZ',\n        oct: 'PAŹ',\n        nov: 'LIS',\n        dec: 'GRU',\n      },\n    },\n    select: {\n      loading: 'Ładowanie',\n      noMatch: 'Brak dopasowań',\n      noData: 'Brak danych',\n      placeholder: 'Wybierz',\n    },\n    mention: {\n      loading: 'Ładowanie',\n    },\n    cascader: {\n      noMatch: 'Brak dopasowań',\n      loading: 'Ładowanie',\n      placeholder: 'Wybierz',\n      noData: 'Brak danych',\n    },\n    pagination: {\n      goto: 'Idź do',\n      pagesize: '/stronę',\n      total: 'Wszystkich {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Wiadomość',\n      confirm: 'OK',\n      cancel: 'Anuluj',\n      error: 'Wiadomość zawiera niedozwolone znaki',\n    },\n    upload: {\n      deleteTip: 'kliknij kasuj aby usunąć',\n      delete: 'Kasuj',\n      preview: 'Podgląd',\n      continue: 'Kontynuuj',\n    },\n    table: {\n      emptyText: 'Brak danych',\n      confirmFilter: 'Potwierdź',\n      resetFilter: 'Resetuj',\n      clearFilter: 'Wszystko',\n      sumText: 'Razem',\n    },\n    tour: {\n      next: 'Dalej',\n      previous: 'Wróć',\n      finish: 'Zakończ',\n    },\n    tree: {\n      emptyText: 'Brak danych',\n    },\n    transfer: {\n      noMatch: 'Brak dopasowań',\n      noData: 'Brak danych',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Wpisz szukaną frazę',\n      noCheckedFormat: 'razem: {total}',\n      hasCheckedFormat: 'wybranych: {checked}/{total}',\n    },\n    image: {\n      error: 'BŁĄD',\n    },\n    pageHeader: {\n      title: 'Wstecz',\n    },\n    popconfirm: {\n      confirmButtonText: 'Tak',\n      cancelButtonText: 'Nie',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC,yBAAyB,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,4BAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,+BAA+B,CAAC,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}