{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/anchor/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Anchor from './src/anchor.vue'\nimport AnchorLink from './src/anchor-link.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAnchor: SFCWithInstall<typeof Anchor> & {\n  AnchorLink: typeof AnchorLink\n} = withInstall(Anchor, {\n  AnchorLink,\n})\nexport const ElAnchorLink: SFCWithInstall<typeof AnchorLink> =\n  withNoopInstall(AnchorLink)\nexport default ElAnchor\n\nexport * from './src/anchor'\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE;AAC5C,EAAE,UAAU;AACZ,CAAC,EAAE;AACS,MAAC,YAAY,GAAG,eAAe,CAAC,UAAU;;;;"}