<template>
  <div class="novel-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>📚 小说列表</h1>
        <p>查看和管理您的小说作品</p>
      </div>
      <div class="header-actions">
        <el-button 
          v-if="novels.length > 0" 
          @click="exportAllNovels"
          :disabled="filteredNovels.length === 0"
        >
          <el-icon><Download /></el-icon>
          导出列表
        </el-button>
        <el-button
          type="success"
          @click="showOneClickGenerateDialog = true"
          class="one-click-generate-btn"
        >
          <el-icon><MagicStick /></el-icon>
          一键生成小说
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建新小说
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
              <el-option label="全部" value="all" />
              <el-option label="创作中" value="writing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已暂停" value="paused" />
            </el-select>
            
            <el-select v-model="genreFilter" placeholder="类型筛选" style="width: 120px;">
              <el-option label="全部类型" value="all" />
              <el-option 
                v-for="(preset, key) in genrePresets" 
                :key="key"
                :label="preset.name" 
                :value="key"
              />
            </el-select>
            
            <el-select v-model="sortBy" placeholder="排序方式" style="width: 140px;">
              <el-option label="最近更新" value="updated" />
              <el-option label="创建时间" value="created" />
              <el-option label="字数" value="wordCount" />
              <el-option label="章节数" value="chapters" />
            </el-select>
          </div>
          
          <div class="filter-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索小说标题、简介..."
              clearable
              style="width: 300px;"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 小说列表 -->
    <div class="novels-grid">
      <div 
        v-for="novel in filteredNovels" 
        :key="novel.id"
        class="novel-card"
      >
        <el-card shadow="hover" class="novel-item">
          <div class="novel-cover">
            <img 
              :src="novel.cover || '/default-cover.jpg'" 
              :alt="novel.title"
              loading="lazy"
              @error="handleImageError"
              @load="handleImageLoad"
            />
            <div class="novel-status">
              <el-tag 
                :type="getStatusType(novel.status)"
                size="small"
              >
                {{ getStatusText(novel.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="novel-info">
            <h3 class="novel-title">{{ novel.title }}</h3>
            <p class="novel-description">{{ novel.description }}</p>
            
            <div class="novel-meta">
              <div class="meta-item">
                <el-icon><Document /></el-icon>
                <span>{{ (novel.chapterList || []).length }}章</span>
              </div>
              <div class="meta-item">
                <el-icon><EditPen /></el-icon>
                <span>{{ formatNumber(novel.wordCount || 0) }}字</span>
              </div>
              <div class="meta-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ formatDate(novel.updatedAt) }}</span>
              </div>
            </div>
            
            <div class="novel-genre">
              <el-tag size="small" type="info">{{ getGenreDisplayName(novel.genre) }}</el-tag>
            </div>
          </div>
          
          <div class="novel-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="openNovel(novel)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button 
              size="small" 
              @click="viewNovelDetails(novel)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-dropdown trigger="click">
              <el-button size="small" type="text">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="editNovelInfo(novel)">
                    <el-icon><EditPen /></el-icon>
                    编辑信息
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="exportNovel(novel)">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-dropdown-item>
                  <el-dropdown-item @click="duplicateNovel(novel)">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="deleteNovel(novel)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredNovels.length === 0" class="empty-state">
      <el-empty description="暂无小说作品">
        <el-button type="primary" @click="showCreateDialog = true">创建第一部小说</el-button>
      </el-empty>
    </div>

    <!-- 创建小说对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新小说"
      width="900px"
      @close="resetCreateForm"
    >
      <div class="create-novel-container">
        <!-- 左侧表单区域 -->
        <div class="form-section">
          <el-form
            ref="createFormRef"
            :model="createForm"
            :rules="createRules"
            label-width="80px"
          >
            <el-form-item label="小说标题" prop="title">
              <el-input v-model="createForm.title" placeholder="请输入小说标题" />
              <div v-if="aiSuggestions.title && !createForm.title" class="ai-suggestion">
                <span class="suggestion-label">AI建议：</span>
                <span class="suggestion-content">{{ aiSuggestions.title }}</span>
                <el-button size="small" text type="primary" @click="acceptSuggestion('title')">
                  采用
                </el-button>
              </div>
            </el-form-item>
        
        <el-form-item label="类型" prop="genre">
          <el-select v-model="createForm.genre" placeholder="请选择小说类型" @change="onGenreChange">
            <el-option 
              v-for="(preset, key) in genrePresets" 
              :key="key"
              :label="preset.name" 
              :value="key"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ preset.name }}</span>
                <el-tag size="small" type="info">{{ preset.tags.slice(0, 2).join('、') }}</el-tag>
              </div>
            </el-option>
          </el-select>
          <div v-if="createForm.genre && genrePresets[createForm.genre]" style="margin-top: 8px; font-size: 12px; color: #909399;">
            💡 {{ genrePresets[createForm.genre].prompt }}
          </div>
          <div v-if="aiSuggestions.genre && !createForm.genre" class="ai-suggestion">
            <span class="suggestion-label">AI建议：</span>
            <span class="suggestion-content">{{ aiSuggestions.genre }}</span>
            <el-button size="small" text type="primary" @click="acceptSuggestion('genre')">
              采用
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="简介" prop="description">
          <div class="description-input-group">
            <el-input 
              v-model="createForm.description" 
              type="textarea" 
              :rows="4"
              placeholder="请输入小说简介或点击AI生成"
            />
            <div class="ai-generate-section" v-if="createForm.genre">
              <el-button 
                type="primary" 
                size="small" 
                @click="generateDescription" 
                :loading="isGeneratingDescription"
                :disabled="!createForm.title?.trim()"
              >
                <el-icon><Star /></el-icon>
                {{ isGeneratingDescription ? 'AI生成中...' : 'AI智能生成' }}
              </el-button>
              <el-button 
                v-if="createForm.description" 
                size="small" 
                @click="generateDescription" 
                :loading="isGeneratingDescription"
                :disabled="!createForm.title?.trim()"
              >
                重新生成
              </el-button>
              <span class="generate-tip">使用AI技术基于标题和类型智能生成</span>
            </div>
          </div>
          <div v-if="aiSuggestions.description && !createForm.description" class="ai-suggestion">
            <span class="suggestion-label">AI建议：</span>
            <div class="suggestion-content description-suggestion">{{ aiSuggestions.description }}</div>
            <el-button size="small" text type="primary" @click="acceptSuggestion('description')">
              采用
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="封面">
          <div class="cover-upload-container">
            <div class="cover-uploader" @click="triggerFileInput">
              <img v-if="createForm.cover" :src="createForm.cover" class="cover-preview" />
              <div v-else class="cover-uploader-placeholder">
                <el-icon class="cover-uploader-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传封面</div>
              </div>
            </div>
            <input 
              ref="fileInput"
              type="file" 
              accept="image/*" 
              style="display: none;"
              @change="handleNativeFileChange"
            />
            <div v-if="createForm.cover" class="cover-actions">
              <el-button size="small" type="danger" @click="removeCover">
                <el-icon><Delete /></el-icon>
                移除封面
              </el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-input 
            v-model="tagInput"
            placeholder="输入标签后按回车添加"
            @keyup.enter="addTag"
          >
            <template #append>
              <el-button @click="addTag">添加</el-button>
            </template>
          </el-input>
          <div class="tags-display" v-if="createForm.tags.length > 0">
            <el-tag 
              v-for="(tag, index) in createForm.tags"
              :key="index"
              closable
              @close="removeTag(index)"
              style="margin: 2px 4px 2px 0;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
        </div>

        <!-- 右侧AI助手区域 -->
        <div class="ai-assistant-section">
          <div class="ai-assistant-header">
            <el-icon><ChatDotRound /></el-icon>
            <span>AI创作助手</span>
            <el-button
              size="small"
              text
              @click="clearCreateChatHistory"
              :disabled="createChatHistory.length === 0"
            >
              清空
            </el-button>
          </div>

          <div class="ai-chat-container">
            <div class="chat-history" ref="createChatHistoryRef">
              <div
                v-for="message in createChatHistory"
                :key="message.id"
                class="chat-message"
                :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
              >
                <div class="message-content">{{ message.content }}</div>
                <div class="message-time">{{ message.timestamp }}</div>
              </div>
              <div v-if="createChatHistory.length === 0" class="empty-chat">
                💡 向AI描述您想要创作的小说，我来帮您填写表单
              </div>
              <div v-if="isCreateChatting" class="typing-indicator">
                <span>AI正在思考</span>
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>

            <div class="chat-input">
              <el-input
                v-model="createChatInput"
                placeholder="例如：我想写一部都市言情小说，讲述一个程序员和设计师的爱情故事..."
                type="textarea"
                :rows="3"
                @keyup.ctrl.enter="sendCreateChatMessage"
              />
              <div class="chat-actions">
                <div class="quick-actions">
                  <el-button
                    size="small"
                    text
                    @click="insertQuickPrompt('帮我想一个小说标题')"
                    :disabled="!!createForm.title"
                  >
                    想标题
                  </el-button>
                  <el-button
                    size="small"
                    text
                    @click="insertQuickPrompt('帮我选择合适的小说类型')"
                    :disabled="!!createForm.genre"
                  >
                    选类型
                  </el-button>
                  <el-button
                    size="small"
                    text
                    @click="insertQuickPrompt('帮我写一个吸引人的简介')"
                    :disabled="!!createForm.description"
                  >
                    写简介
                  </el-button>
                  <el-button
                    size="small"
                    text
                    @click="insertQuickPrompt('帮我完整填写这个小说的所有信息')"
                    v-if="!createForm.title && !createForm.genre && !createForm.description"
                  >
                    全部填写
                  </el-button>
                </div>
                <el-button
                  type="primary"
                  @click="sendCreateChatMessage"
                  :loading="isCreateChatting"
                  :disabled="!createChatInput.trim()"
                >
                  发送 (Ctrl+Enter)
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createNovel">创建</el-button>
      </template>
    </el-dialog>

    <!-- 一键生成小说对话框 -->
    <el-dialog
      v-model="showOneClickGenerateDialog"
      title="🎯 一键生成完整小说"
      width="800px"
      @close="resetOneClickGenerate"
    >
      <div class="one-click-generate-container">
        <el-steps :active="currentStep" finish-status="success" class="generation-steps">
          <el-step title="基础设定" description="填写小说基本信息" />
          <el-step title="AI生成" description="智能生成小说内容" />
          <el-step title="完成创建" description="保存并跳转编辑" />
        </el-steps>

        <!-- 步骤1: 基础设定 -->
        <div v-if="currentStep === 0" class="step-content">
          <el-form :model="oneClickForm" label-width="120px" class="one-click-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="小说标题" required>
                  <el-input
                    v-model="oneClickForm.title"
                    placeholder="请输入小说标题"
                    maxlength="50"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="小说类型" required>
                  <el-select v-model="oneClickForm.genre" placeholder="选择小说类型">
                    <el-option label="玄幻修仙" value="玄幻修仙" />
                    <el-option label="都市言情" value="都市言情" />
                    <el-option label="科幻未来" value="科幻未来" />
                    <el-option label="历史穿越" value="历史穿越" />
                    <el-option label="悬疑推理" value="悬疑推理" />
                    <el-option label="武侠江湖" value="武侠江湖" />
                    <el-option label="奇幻冒险" value="奇幻冒险" />
                    <el-option label="军事战争" value="军事战争" />
                    <el-option label="商业职场" value="商业职场" />
                    <el-option label="校园青春" value="校园青春" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="主题关键词" required>
              <el-input
                v-model="oneClickForm.theme"
                placeholder="例如：复仇、成长、爱情、权力斗争等"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="主角设定" required>
              <el-input
                v-model="oneClickForm.protagonist"
                type="textarea"
                :rows="3"
                placeholder="简单描述主角的身份、性格、背景等"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="故事背景" required>
              <el-input
                v-model="oneClickForm.background"
                type="textarea"
                :rows="3"
                placeholder="描述故事发生的时代、地点、环境等"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="章节数量">
                  <el-input-number
                    v-model="oneClickForm.chapterCount"
                    :min="10"
                    :max="1000"
                    :step="10"
                  />
                  <span class="form-tip">长篇小说建议100-500章</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="人物数量">
                  <el-input-number
                    v-model="oneClickForm.characterCount"
                    :min="5"
                    :max="100"
                    :step="5"
                  />
                  <span class="form-tip">长篇小说建议20-50个人物</span>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="生成内容">
              <el-checkbox-group v-model="oneClickForm.generateTypes">
                <el-checkbox label="chapters">章节大纲</el-checkbox>
                <el-checkbox label="characters">人物设定</el-checkbox>
                <el-checkbox label="worldview">世界观设定</el-checkbox>
                <el-checkbox label="events">事件线</el-checkbox>
                <el-checkbox label="corpus">语料库</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <!-- 长篇小说提示 -->
            <el-alert
              v-if="oneClickForm.chapterCount >= 100"
              title="长篇小说创作建议"
              type="info"
              :closable="false"
              class="long-novel-tip"
            >
              <template #default>
                <div class="tip-content">
                  <p><strong>🎯 几百万字长篇小说规划：</strong></p>
                  <ul>
                    <li>• <strong>字数规划</strong>：{{ oneClickForm.chapterCount }}章 × 3000字/章 ≈ {{ Math.floor(oneClickForm.chapterCount * 3000 / 10000) }}万字</li>
                    <li>• <strong>更新节奏</strong>：建议每日更新1-2章，保持连载热度</li>
                    <li>• <strong>故事结构</strong>：分为多个大的故事阶段，每个阶段有独立的高潮和转折</li>
                    <li>• <strong>人物发展</strong>：{{ oneClickForm.characterCount }}个人物足够支撑复杂的关系网络和多线剧情</li>
                    <li>• <strong>世界观扩展</strong>：为后续剧情发展预留足够的世界观扩展空间</li>
                  </ul>
                  <p class="tip-highlight">💡 AI将为您生成适合长篇连载的详细框架，包含多层次的故事结构和人物关系网络。</p>
                </div>
              </template>
            </el-alert>

            <!-- 分批生成提示 -->
            <el-alert
              v-if="oneClickForm.chapterCount > 50 || oneClickForm.characterCount > 20"
              title="智能分批生成"
              type="success"
              :closable="false"
              class="batch-tip"
            >
              <template #default>
                <div class="tip-content">
                  <p><strong>🚀 分批生成策略：</strong></p>
                  <ul>
                    <li v-if="oneClickForm.chapterCount > 50">
                      • <strong>章节分批</strong>：{{ oneClickForm.chapterCount }}章将分为{{ Math.ceil(oneClickForm.chapterCount / 50) }}批生成，每批最多50章
                    </li>
                    <li v-if="oneClickForm.characterCount > 20">
                      • <strong>人物分批</strong>：{{ oneClickForm.characterCount }}个人物将分为{{ Math.ceil(oneClickForm.characterCount / 20) }}批生成，每批最多20个人物
                    </li>
                    <li>• <strong>提高成功率</strong>：分批生成避免单次请求过大，提高生成成功率</li>
                    <li>• <strong>详细进度</strong>：可实时查看每批的生成进度和状态</li>
                    <li>• <strong>错误恢复</strong>：单批失败不影响其他批次，可单独重试</li>
                  </ul>
                  <p class="tip-highlight">⚡ 分批生成让大型小说创作更稳定、更高效！</p>
                </div>
              </template>
            </el-alert>
          </el-form>
        </div>

        <!-- 步骤2: AI生成 -->
        <div v-if="currentStep === 1" class="step-content">
          <div class="generation-progress">
            <el-card shadow="never">
              <template #header>
                <div class="progress-header">
                  <el-icon class="rotating"><Loading /></el-icon>
                  <span>正在生成中...</span>
                </div>
              </template>
              <el-progress
                :percentage="generationProgress"
                :stroke-width="8"
                :show-text="true"
                status="success"
              />
              <div class="progress-text">{{ currentGenerationStep }}</div>

              <!-- 生成日志 -->
              <div class="generation-log">
                <div
                  v-for="(log, index) in generationLogs"
                  :key="index"
                  class="log-item"
                  :class="{ 'completed': log.completed, 'current': log.current }"
                >
                  <el-icon v-if="log.completed"><Check /></el-icon>
                  <el-icon v-else-if="log.current" class="rotating"><Loading /></el-icon>
                  <el-icon v-else><Clock /></el-icon>
                  <span>{{ log.text }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 步骤3: 完成创建 -->
        <div v-if="currentStep === 2" class="step-content">
          <div class="generation-result">
            <el-result
              icon="success"
              title="小说生成完成！"
              sub-title="AI已为您生成完整的小说框架，现在可以开始创作了"
            >
              <template #extra>
                <div class="result-summary">
                  <el-tag v-if="generationResult.chapters" type="success" size="large">
                    📚 章节: {{ generationResult.chapters }}个
                  </el-tag>
                  <el-tag v-if="generationResult.characters" type="primary" size="large">
                    👥 人物: {{ generationResult.characters }}个
                  </el-tag>
                  <el-tag v-if="generationResult.worldview" type="info" size="large">
                    🌍 世界观: {{ generationResult.worldview }}个
                  </el-tag>
                  <el-tag v-if="generationResult.events" type="warning" size="large">
                    📅 事件: {{ generationResult.events }}个
                  </el-tag>
                  <el-tag v-if="generationResult.corpus" type="danger" size="large">
                    📝 语料: {{ generationResult.corpus }}个
                  </el-tag>
                </div>
              </template>
            </el-result>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="currentStep > 0"
            @click="previousStep"
            :disabled="currentStep === 1 && isGenerating"
          >
            上一步
          </el-button>
          <el-button @click="showOneClickGenerateDialog = false" :disabled="isGenerating">
            取消
          </el-button>
          <el-button
            v-if="currentStep === 0"
            type="primary"
            @click="nextStep"
            :disabled="!canProceedToGeneration"
          >
            下一步
          </el-button>
          <el-button
            v-if="currentStep === 1"
            type="primary"
            @click="startGeneration"
            :loading="isGenerating"
            :disabled="isGenerating"
          >
            开始生成
          </el-button>
          <el-button
            v-if="currentStep === 2"
            type="success"
            @click="finishAndJumpToEditor"
          >
            进入编辑器
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 小说详情对话框 -->
    <el-dialog 
      v-model="showDetailsDialog" 
      title="小说详情" 
      width="800px"
    >
      <div v-if="selectedNovel" class="novel-details">
        <div class="details-header">
          <div class="details-cover">
            <img 
              :src="selectedNovel.cover || '/default-cover.jpg'" 
              :alt="selectedNovel.title"
              loading="lazy"
              @error="handleImageError"
              @load="handleImageLoad"
            />
          </div>
          <div class="details-info">
            <h2>{{ selectedNovel.title }}</h2>
            <p class="details-description">{{ selectedNovel.description }}</p>
            <div class="details-meta">
              <div class="meta-row">
                <span class="meta-label">类型：</span>
                <el-tag size="small">{{ getGenreDisplayName(selectedNovel.genre) }}</el-tag>
              </div>
              <div class="meta-row">
                <span class="meta-label">状态：</span>
                <el-tag :type="getStatusType(selectedNovel.status)" size="small">
                  {{ getStatusText(selectedNovel.status) }}
                </el-tag>
              </div>
              <div class="meta-row">
                <span class="meta-label">章节：</span>
                <span>{{ selectedNovel.chapters }}章</span>
              </div>
              <div class="meta-row">
                <span class="meta-label">字数：</span>
                <span>{{ formatNumber(selectedNovel.wordCount) }}字</span>
              </div>
              <div class="meta-row">
                <span class="meta-label">创建时间：</span>
                <span>{{ formatDate(selectedNovel.createdAt) }}</span>
              </div>
              <div class="meta-row">
                <span class="meta-label">更新时间：</span>
                <span>{{ formatDate(selectedNovel.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="details-content">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="章节列表" name="chapters">
              <div class="chapters-list">
                <div 
                  v-for="(chapter, index) in selectedNovel.chapterList" 
                  :key="index"
                  class="chapter-item"
                >
                  <div class="chapter-info">
                    <h4>第{{ index + 1 }}章 {{ chapter.title }}</h4>
                    <p>{{ chapter.wordCount }}字 · {{ formatDate(chapter.updatedAt) }}</p>
                  </div>
                  <div class="chapter-actions">
                    <el-button size="small" @click="editChapter(chapter)">编辑</el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="创作记录" name="records">
              <div class="writing-records">
                <div 
                  v-for="record in selectedNovel.writingRecords" 
                  :key="record.id"
                  class="record-item"
                >
                  <div class="record-date">{{ formatDate(record.date) }}</div>
                  <div class="record-content">
                    <div class="record-stats">
                      <span>写作 {{ record.wordsWritten }} 字</span>
                      <span>用时 {{ record.timeSpent }} 分钟</span>
                    </div>
                    <div class="record-note" v-if="record.note">
                      {{ record.note }}
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="统计数据" name="statistics">
              <div class="novel-statistics">
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-value">{{ selectedNovel.totalWords }}</div>
                    <div class="stat-label">总字数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ (selectedNovel.chapterList || []).length }}</div>
                    <div class="stat-label">章节数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ Math.round((selectedNovel.wordCount || 0) / Math.max((selectedNovel.chapterList || []).length, 1)) }}</div>
                    <div class="stat-label">平均章节字数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ selectedNovel.writingDays }}</div>
                    <div class="stat-label">创作天数</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑小说信息对话框 -->
    <el-dialog 
      v-model="showEditDialog" 
      title="编辑小说信息" 
      width="600px"
      @close="resetEditForm"
    >
      <el-form 
        ref="editFormRef" 
        :model="editForm" 
        :rules="editRules" 
        label-width="80px"
      >
        <el-form-item label="小说标题" prop="title">
          <el-input v-model="editForm.title" placeholder="请输入小说标题" />
        </el-form-item>
        
        <el-form-item label="类型" prop="genre">
          <el-select v-model="editForm.genre" placeholder="请选择小说类型" @change="onEditGenreChange">
            <el-option 
              v-for="(preset, key) in genrePresets" 
              :key="key"
              :label="preset.name" 
              :value="key"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ preset.name }}</span>
                <el-tag size="small" type="info">{{ preset.tags.slice(0, 2).join('、') }}</el-tag>
              </div>
            </el-option>
          </el-select>
          <div v-if="editForm.genre && genrePresets[editForm.genre]" style="margin-top: 8px; font-size: 12px; color: #909399;">
            💡 {{ genrePresets[editForm.genre].prompt }}
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="editForm.status" placeholder="请选择小说状态">
            <el-option label="创作中" value="writing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已暂停" value="paused" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="简介" prop="description">
          <div class="description-input-group">
            <el-input 
              v-model="editForm.description" 
              type="textarea" 
              :rows="4"
              placeholder="请输入小说简介或点击AI生成"
            />
            <div class="ai-generate-section" v-if="editForm.genre">
              <el-button 
                type="primary" 
                size="small" 
                @click="generateEditDescription" 
                :loading="isGeneratingEditDescription"
                :disabled="!editForm.title?.trim()"
              >
                <el-icon><Star /></el-icon>
                {{ isGeneratingEditDescription ? 'AI生成中...' : 'AI重新生成' }}
              </el-button>
              <span class="generate-tip">使用AI技术基于标题和类型智能生成</span>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="封面">
          <div class="cover-upload-container">
            <div class="cover-uploader" @click="triggerEditFileInput">
              <img v-if="editForm.cover" :src="editForm.cover" class="cover-preview" />
              <div v-else class="cover-uploader-placeholder">
                <el-icon class="cover-uploader-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传封面</div>
              </div>
            </div>
            <input 
              ref="editFileInput"
              type="file" 
              accept="image/*" 
              style="display: none;"
              @change="handleEditFileChange"
            />
            <div v-if="editForm.cover" class="cover-actions">
              <el-button size="small" type="danger" @click="removeEditCover">
                <el-icon><Delete /></el-icon>
                移除封面
              </el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-input 
            v-model="editTagInput"
            placeholder="输入标签后按回车添加"
            @keyup.enter="addEditTag"
          >
            <template #append>
              <el-button @click="addEditTag">添加</el-button>
            </template>
          </el-input>
          <div class="tags-display" v-if="editForm.tags.length > 0">
            <el-tag 
              v-for="(tag, index) in editForm.tags"
              :key="index"
              closable
              @close="removeEditTag(index)"
              style="margin: 2px 4px 2px 0;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateNovelInfo" :loading="isSavingEdit">保存修改</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Plus, Search, Document, EditPen, Calendar, Edit, View,
  MoreFilled, Star, Download, CopyDocument, Delete, Close, ChatDotRound,
  MagicStick, Loading, Check, Clock
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import apiService from '@/services/api.js'

const router = useRouter()

// 响应式数据
const statusFilter = ref('all')
const genreFilter = ref('all')
const sortBy = ref('updated')
const searchKeyword = ref('')
const showCreateDialog = ref(false)
const showDetailsDialog = ref(false)
const showEditDialog = ref(false)
const selectedNovel = ref(null)
const editingNovel = ref(null)
const activeTab = ref('chapters')
const tagInput = ref('')
const editTagInput = ref('')
const createFormRef = ref()
const editFormRef = ref()
const editFileInput = ref()
const isGeneratingDescription = ref(false)
const isGeneratingEditDescription = ref(false)
const isSavingEdit = ref(false)

// 一键生成相关数据
const showOneClickGenerateDialog = ref(false)
const currentStep = ref(0)
const isGenerating = ref(false)
const generationProgress = ref(0)
const currentGenerationStep = ref('')
const generationLogs = ref([])
const generationResult = ref(null)

// 一键生成表单
const oneClickForm = ref({
  title: '',
  genre: '',
  theme: '',
  protagonist: '',
  background: '',
  chapterCount: 100,  // 默认100章，适合长篇小说
  characterCount: 20, // 默认20个人物
  generateTypes: ['chapters', 'characters', 'worldview', 'events', 'corpus']
})

// AI聊天相关数据
const createChatHistory = ref([])
const createChatInput = ref('')
const isCreateChatting = ref(false)
const createChatHistoryRef = ref()

// AI建议数据
const aiSuggestions = ref({
  title: '',
  genre: '',
  genreKey: '', // 保存类型的key值
  description: ''
})

// 小说数据 - 从localStorage加载
const novels = ref([])

// 加载小说数据
const loadNovels = () => {
  try {
    const saved = localStorage.getItem('novels')
    if (saved) {
      const parsedNovels = JSON.parse(saved)
      // 将日期字符串转换为Date对象
      novels.value = parsedNovels.map(novel => ({
        ...novel,
        createdAt: new Date(novel.createdAt),
        updatedAt: new Date(novel.updatedAt),
        chapterList: (novel.chapterList || []).map(chapter => ({
          ...chapter,
          createdAt: chapter.createdAt ? new Date(chapter.createdAt) : new Date(),
          updatedAt: chapter.updatedAt ? new Date(chapter.updatedAt) : new Date()
        })),
        writingRecords: (novel.writingRecords || []).map(record => ({
          ...record,
          date: new Date(record.date)
        }))
      }))
    } else {
      // 如果没有保存的数据，初始化为空
      novels.value = []
      // 保存空数据到localStorage
      saveNovels()
    }
  } catch (error) {
    console.error('加载小说数据失败:', error)
    novels.value = []
  }
}

// 保存小说数据到localStorage
const saveNovels = () => {
  try {
    localStorage.setItem('novels', JSON.stringify(novels.value))
  } catch (error) {
    console.error('保存小说数据失败:', error)
    ElMessage.error('保存数据失败')
  }
}

// 创建表单
const createForm = ref({
  title: '',
  genre: '',
  description: '',
  cover: '',
  tags: []
})

// 编辑表单
const editForm = ref({
  title: '',
  genre: '',
  status: '',
  description: '',
  cover: '',
  tags: []
})

// 动态类型预设配置 - 从localStorage读取
const genrePresets = ref({})

// 表单验证规则
const createRules = {
  title: [{ required: true, message: '请输入小说标题', trigger: 'blur' }],
  genre: [{ required: true, message: '请选择小说类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入小说简介', trigger: 'blur' }]
}

const editRules = {
  title: [{ required: true, message: '请输入小说标题', trigger: 'blur' }],
  genre: [{ required: true, message: '请选择小说类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择小说状态', trigger: 'change' }],
  description: [{ required: true, message: '请输入小说简介', trigger: 'blur' }]
}

// 计算属性
const filteredNovels = computed(() => {
  let result = novels.value
  
  // 状态筛选
  if (statusFilter.value !== 'all') {
    result = result.filter(novel => novel.status === statusFilter.value)
  }
  
  // 类型筛选
  if (genreFilter.value !== 'all') {
    result = result.filter(novel => novel.genre === genreFilter.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(novel => 
      novel.title.toLowerCase().includes(keyword) ||
      novel.description.toLowerCase().includes(keyword)
    )
  }
  
  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'updated':
        return new Date(b.updatedAt) - new Date(a.updatedAt)
      case 'created':
        return new Date(b.createdAt) - new Date(a.createdAt)
      case 'wordCount':
        return b.wordCount - a.wordCount
      case 'chapters':
        return b.chapters - a.chapters
      default:
        return 0
    }
  })
  
  return result
})

// 方法
const getStatusType = (status) => {
  const types = {
    writing: 'success',
    completed: 'info',
    paused: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    writing: '创作中',
    completed: '已完成',
    paused: '已暂停'
  }
  return texts[status] || '未知'
}

const getGenreDisplayName = (genreCode) => {
  return genrePresets.value[genreCode]?.name || genreCode || '未知'
}

// 加载类型数据
const loadGenres = () => {
  try {
    const saved = localStorage.getItem('novelGenres')
    if (saved) {
      const parsed = JSON.parse(saved)
      // 转换为键值对格式，兼容旧版本
      const genresObj = {}
      parsed.forEach(genre => {
        genresObj[genre.code] = {
          name: genre.name,
          tags: genre.tags,
          prompt: genre.prompt
        }
      })
      genrePresets.value = genresObj
    } else {
      // 如果没有保存的数据，加载默认类型
      loadDefaultGenres()
    }
  } catch (error) {
    console.error('加载类型数据失败:', error)
    loadDefaultGenres()
  }
}

// 加载默认类型
const loadDefaultGenres = () => {
  const defaultGenres = {
    fantasy: {
      name: '玄幻',
      tags: ['修仙', '异世界', '法宝', '灵气', '境界'],
      prompt: '创作一部玄幻小说，包含修仙体系、异世界冒险等元素，注重世界观构建和修炼体系描写。'
    },
    urban: {
      name: '都市',
      tags: ['都市', '现代', '职场', '生活'],
      prompt: '创作一部都市小说，以现代都市为背景，贴近现实生活，注重人物情感和社会现象描写。'
    },
    history: {
      name: '历史',
      tags: ['历史', '古代', '朝廷', '战争'],
      prompt: '创作一部历史小说，以真实历史为背景，注重历史考证和时代特色描写。'
    },
    scifi: {
      name: '科幻',
      tags: ['科幻', '未来', '科技', '太空'],
      prompt: '创作一部科幻小说，包含未来科技、太空探索等元素，注重科学性和想象力的平衡。'
    },
    wuxia: {
      name: '武侠',
      tags: ['武侠', '江湖', '武功', '侠义'],
      prompt: '创作一部武侠小说，以江湖为背景，注重武功描写和侠义精神体现。'
    },
    romance: {
      name: '言情',
      tags: ['言情', '爱情', '情感', '浪漫'],
      prompt: '创作一部言情小说，以爱情为主线，注重情感描写和人物关系发展。'
    }
  }
  genrePresets.value = defaultGenres
}

// 更新类型使用计数
const updateGenreUsageCount = (genreCode) => {
  try {
    const saved = localStorage.getItem('novelGenres')
    if (saved) {
      const genres = JSON.parse(saved)
      const genreIndex = genres.findIndex(g => g.code === genreCode)
      if (genreIndex > -1) {
        genres[genreIndex].usageCount = (genres[genreIndex].usageCount || 0) + 1
        localStorage.setItem('novelGenres', JSON.stringify(genres))
        console.log(`类型 ${genreCode} 使用计数更新为:`, genres[genreIndex].usageCount)
      }
    }
  } catch (error) {
    console.error('更新类型使用计数失败:', error)
  }
}

const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const handleImageError = (e) => {
  // 防止无限循环加载
  if (e.target.src.includes('default-cover.jpg') || e.target.getAttribute('data-error-handled')) {
    // 如果默认图片也加载失败，显示占位符
    e.target.style.display = 'none'
    
    // 检查是否已经有占位符，避免重复创建
    const existingPlaceholder = e.target.parentNode.querySelector('.image-placeholder')
    if (!existingPlaceholder) {
      const placeholder = document.createElement('div')
      placeholder.className = 'image-placeholder'
      placeholder.innerHTML = '<i class="el-icon-picture"></i><span>暂无封面</span>'
      e.target.parentNode.appendChild(placeholder)
    }
    return
  }
  
  // 标记已经尝试过加载默认图片
  e.target.setAttribute('data-error-handled', 'true')
  e.target.src = '/default-cover.jpg'
}

const handleImageLoad = (e) => {
  // 图片加载成功，移除错误标记
  e.target.removeAttribute('data-error-handled')
  
  // 移除可能存在的占位符
  const placeholder = e.target.parentNode.querySelector('.image-placeholder')
  if (placeholder) {
    placeholder.remove()
  }
}

const openNovel = (novel) => {
  // 跳转到AI写作页面
  router.push(`/writer?novelId=${novel.id}`)
}

const viewNovelDetails = (novel) => {
  selectedNovel.value = novel
  showDetailsDialog.value = true
}

const exportNovel = (novel) => {
  try {
    // 简化的HTML清理函数
    const cleanHtml = (htmlString) => {
      if (!htmlString) return ''
      return htmlString
        .replace(/<br\s*\/?>/gi, '\n')  // br标签转换为换行
        .replace(/<\/p>/gi, '\n\n')     // p结束标签转换为双换行
        .replace(/<[^>]*>/g, '')        // 移除所有HTML标签
        .replace(/&nbsp;/g, ' ')        // HTML空格转换为普通空格
        .replace(/&lt;/g, '<')          // HTML实体转换
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\n\s*\n\s*\n+/g, '\n\n') // 清理多余换行
        .trim()
    }
    
    // 构建导出内容
    let exportContent = `《${novel.title}》\n`
    exportContent += `${'='.repeat(50)}\n\n`
    
    // 基本信息
    exportContent += `📚 小说信息\n`
    exportContent += `标题：${novel.title}\n`
    exportContent += `作者：${novel.author || '未设置'}\n`
    exportContent += `类型：${getGenreDisplayName(novel.genre)}\n`
    exportContent += `状态：${getStatusText(novel.status)}\n`
    exportContent += `字数：${formatNumber(novel.wordCount || 0)}字\n`
    exportContent += `章节：${novel.chapters || 0}章\n`
    exportContent += `创建时间：${formatDate(novel.createdAt)}\n`
    exportContent += `更新时间：${formatDate(novel.updatedAt)}\n`
    
    if (novel.tags && novel.tags.length > 0) {
      exportContent += `标签：${novel.tags.join('、')}\n`
    }
    
    if (novel.description) {
      exportContent += `\n📖 简介\n`
      exportContent += `${cleanHtml(novel.description)}\n`
    }
    
    exportContent += `\n${'='.repeat(50)}\n\n`
    
    // 章节内容
    if (novel.chapterList && novel.chapterList.length > 0) {
      exportContent += `📝 章节内容\n\n`
      
      novel.chapterList.forEach((chapter, index) => {
        exportContent += `第${index + 1}章 ${chapter.title}\n`
        exportContent += `${'-'.repeat(30)}\n\n`
        
        if (chapter.description) {
          exportContent += `【章节简介】\n${cleanHtml(chapter.description)}\n\n`
        }
        
        if (chapter.content) {
          const cleanContent = cleanHtml(chapter.content)
          exportContent += `${cleanContent}\n\n`
        } else {
          exportContent += `（章节内容暂无）\n\n`
        }
        
        exportContent += `字数：${chapter.wordCount || 0}字\n`
        exportContent += `更新时间：${formatDate(chapter.updatedAt || chapter.createdAt)}\n\n`
        exportContent += `${'='.repeat(50)}\n\n`
      })
    } else {
      exportContent += `📝 章节内容\n\n`
      exportContent += `暂无章节内容\n\n`
    }
    
    // 统计信息
    exportContent += `📊 创作统计\n`
    exportContent += `总字数：${formatNumber(novel.totalWords || novel.wordCount || 0)}字\n`
    exportContent += `平均章节字数：${novel.avgWordsPerChapter || 0}字\n`
    exportContent += `创作天数：${novel.writingDays || 0}天\n`
    
    if (novel.writingRecords && novel.writingRecords.length > 0) {
      exportContent += `\n📝 创作记录\n`
      novel.writingRecords.forEach(record => {
        exportContent += `${formatDate(record.date)}：写作${record.wordsWritten}字，用时${record.timeSpent}分钟\n`
        if (record.note) {
          exportContent += `备注：${cleanHtml(record.note)}\n`
        }
      })
    }
    
    exportContent += `\n\n导出时间：${new Date().toLocaleString()}\n`
    exportContent += `导出来源：AI小说生成器v0.5.0\n`
    
    // 创建并下载文件
    const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // 文件名处理
    const safeTitle = novel.title.replace(/[<>:"/\\|?*]/g, '_')
    link.download = `${safeTitle}_${new Date().toISOString().slice(0, 10)}.txt`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success(`《${novel.title}》导出成功！`)
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 批量导出所有小说
const exportAllNovels = () => {
  try {
    if (filteredNovels.value.length === 0) {
      ElMessage.warning('没有可导出的小说')
      return
    }
    
    // 简化的HTML清理函数
    const cleanHtml = (htmlString) => {
      if (!htmlString) return ''
      return htmlString
        .replace(/<br\s*\/?>/gi, '\n')  // br标签转换为换行
        .replace(/<\/p>/gi, '\n\n')     // p结束标签转换为双换行
        .replace(/<[^>]*>/g, '')        // 移除所有HTML标签
        .replace(/&nbsp;/g, ' ')        // HTML空格转换为普通空格
        .replace(/&lt;/g, '<')          // HTML实体转换
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\n\s*\n\s*\n+/g, '\n\n') // 清理多余换行
        .trim()
    }
    
    // 构建导出内容
    let exportContent = `📚 小说列表导出\n`
    exportContent += `${'='.repeat(60)}\n\n`
    exportContent += `导出时间：${new Date().toLocaleString()}\n`
    exportContent += `小说数量：${filteredNovels.value.length}部\n`
    exportContent += `导出来源：AI小说生成器v0.5.0\n\n`
    exportContent += `${'='.repeat(60)}\n\n`
    
    filteredNovels.value.forEach((novel, index) => {
      exportContent += `【第${index + 1}部】《${novel.title}》\n`
      exportContent += `${'='.repeat(50)}\n\n`
      
      // 基本信息
      exportContent += `📚 小说信息\n`
      exportContent += `标题：${novel.title}\n`
      exportContent += `作者：${novel.author || '未设置'}\n`
      exportContent += `类型：${getGenreDisplayName(novel.genre)}\n`
      exportContent += `状态：${getStatusText(novel.status)}\n`
      exportContent += `字数：${formatNumber(novel.wordCount || 0)}字\n`
      exportContent += `章节：${novel.chapters || 0}章\n`
      exportContent += `创建时间：${formatDate(novel.createdAt)}\n`
      exportContent += `更新时间：${formatDate(novel.updatedAt)}\n`
      
      if (novel.tags && novel.tags.length > 0) {
        exportContent += `标签：${novel.tags.join('、')}\n`
      }
      
      if (novel.description) {
        exportContent += `\n📖 简介\n`
        exportContent += `${cleanHtml(novel.description)}\n`
      }
      
      exportContent += `\n${'='.repeat(50)}\n\n`
      
      // 章节概要
      if (novel.chapterList && novel.chapterList.length > 0) {
        exportContent += `📝 章节概要\n`
        novel.chapterList.forEach((chapter, chapterIndex) => {
          exportContent += `第${chapterIndex + 1}章 ${chapter.title}`
          if (chapter.wordCount) {
            exportContent += ` (${chapter.wordCount}字)`
          }
          exportContent += `\n`
          if (chapter.description) {
            exportContent += `  简介：${cleanHtml(chapter.description)}\n`
          }
        })
        exportContent += `\n`
      } else {
        exportContent += `📝 章节概要\n`
        exportContent += `暂无章节内容\n\n`
      }
      
      // 统计信息
      exportContent += `📊 创作统计\n`
      exportContent += `总字数：${formatNumber(novel.totalWords || novel.wordCount || 0)}字\n`
      exportContent += `平均章节字数：${novel.avgWordsPerChapter || 0}字\n`
      exportContent += `创作天数：${novel.writingDays || 0}天\n\n`
      
      // 分隔符
      if (index < filteredNovels.value.length - 1) {
        exportContent += `\n${'#'.repeat(60)}\n\n`
      }
    })
    
    exportContent += `\n\n${'='.repeat(60)}\n`
    exportContent += `导出完成！共导出 ${filteredNovels.value.length} 部小说\n`
    exportContent += `感谢使用 AI小说生成器v0.5.0\n`
    
    // 创建并下载文件
    const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // 生成文件名
    const dateStr = new Date().toISOString().slice(0, 10)
    const statusText = statusFilter.value === 'all' ? '全部' : getStatusText(statusFilter.value)
    const genreText = genreFilter.value === 'all' ? '全部类型' : (genrePresets.value[genreFilter.value]?.name || '未知类型')
    
    link.download = `小说列表_${statusText}_${genreText}_${dateStr}.txt`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success(`成功导出 ${filteredNovels.value.length} 部小说！`)
    
  } catch (error) {
    console.error('批量导出失败:', error)
    ElMessage.error('批量导出失败，请重试')
  }
}

const duplicateNovel = (novel) => {
  const newNovel = {
    ...novel,
    id: Date.now(),
    title: novel.title + ' (副本)',
    createdAt: new Date(),
    updatedAt: new Date()
  }
  novels.value.push(newNovel)
  // 保存到localStorage
  saveNovels()
  ElMessage.success('小说复制成功')
}

const deleteNovel = async (novel) => {
  try {
    await ElMessageBox.confirm(`确定要删除《${novel.title}》吗？此操作不可恢复。`, '确认删除', {
      type: 'warning'
    })
    
    const index = novels.value.findIndex(n => n.id === novel.id)
    if (index > -1) {
      novels.value.splice(index, 1)
      // 保存到localStorage
      saveNovels()
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const addTag = () => {
  if (tagInput.value.trim() && !createForm.value.tags.includes(tagInput.value.trim())) {
    createForm.value.tags.push(tagInput.value.trim())
    tagInput.value = ''
  }
}

const removeTag = (index) => {
  createForm.value.tags.splice(index, 1)
}

const fileInput = ref()

const triggerFileInput = () => {
  console.log('触发文件选择器')
  fileInput.value?.click()
}

const handleNativeFileChange = (event) => {
  const file = event.target.files[0]
  console.log('原生文件选择事件触发:', file)
  
  if (!file) {
    console.log('没有选择文件')
    return
  }
  
  console.log('文件信息:', {
    name: file.name,
    type: file.type,
    size: file.size
  })
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('只能上传图片文件!')
    console.log('文件类型验证失败:', file.type)
    return
  }
  
  // 验证文件大小（2MB）
  if (file.size / 1024 / 1024 > 2) {
    ElMessage.error('图片大小不能超过 2MB!')
    console.log('文件大小验证失败:', (file.size / 1024 / 1024).toFixed(2) + 'MB')
    return
  }
  
  console.log('开始读取文件为base64...')
  
  // 转换为base64格式保存
  const reader = new FileReader()
  reader.onload = (e) => {
    console.log('FileReader读取成功')
    createForm.value.cover = e.target.result // base64字符串
    ElMessage.success('封面上传成功!')
    console.log('封面base64长度:', e.target.result.length)
    console.log('封面已保存到createForm.cover')
    
    // 清空input的值，这样可以重复选择同一个文件
    event.target.value = ''
  }
  reader.onerror = (e) => {
    console.error('FileReader读取失败:', e)
    ElMessage.error('封面读取失败，请重试')
  }
  
  // 读取文件为base64
  reader.readAsDataURL(file)
}

const handleCoverSuccess = (response, file) => {
  // 这个函数现在不会被调用，因为我们阻止了默认上传
  // 但保留以备后续扩展
}

const removeCover = () => {
  createForm.value.cover = ''
  ElMessage.success('封面已移除')
}

const createNovel = async () => {
  try {
    await createFormRef.value.validate()
    
    const newNovel = {
      ...createForm.value,
      id: Date.now(),
      status: 'writing',
      chapters: 0,
      wordCount: 0,
      totalWords: 0,
      avgWordsPerChapter: 0,
      writingDays: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      chapterList: [],
      writingRecords: [],
      genrePrompt: genrePresets[createForm.value.genre]?.prompt || '',
      // 章节管理需要的数据结构
      characters: [],
      worldSettings: [],
      corpusData: [],
      events: []
    }
    
    novels.value.unshift(newNovel)
    
    // 更新类型使用计数
    updateGenreUsageCount(createForm.value.genre)
    
    // 保存到localStorage
    saveNovels()
    
    ElMessage.success('小说创建成功！即将跳转到编辑区...')
    showCreateDialog.value = false
    resetCreateForm()
    
    // 创建成功后跳转到编辑页面
    setTimeout(() => {
      router.push(`/writer?novelId=${newNovel.id}`)
    }, 1000)
  } catch (error) {
    console.error('创建小说失败:', error)
    ElMessage.error('创建小说失败')
  }
}

// 监听类型选择，自动填充标签
const onGenreChange = (genre) => {
  if (genrePresets.value[genre]) {
    createForm.value.tags = [...genrePresets.value[genre].tags]
  }
}

const resetCreateForm = () => {
  createForm.value = {
    title: '',
    genre: '',
    description: '',
    cover: '',
    tags: []
  }
  tagInput.value = ''

  // 清空AI建议和聊天历史
  aiSuggestions.value = {
    title: '',
    genre: '',
    genreKey: '',
    description: ''
  }
  createChatHistory.value = []
  createChatInput.value = ''
}

// 编辑小说信息
const editNovelInfo = (novel) => {
  editingNovel.value = novel
  editForm.value = {
    title: novel.title,
    genre: novel.genre,
    status: novel.status,
    description: novel.description,
    cover: novel.cover || '',
    tags: [...(novel.tags || [])]
  }
  showEditDialog.value = true
}

// 重置编辑表单
const resetEditForm = () => {
  editForm.value = {
    title: '',
    genre: '',
    status: '',
    description: '',
    cover: '',
    tags: []
  }
  editTagInput.value = ''
  editingNovel.value = null
  editFormRef.value?.clearValidate()
}

// 编辑表单的类型变化处理
const onEditGenreChange = (genre) => {
  // 可以选择是否自动更新标签，这里不自动更新，让用户手动调整
}

// 添加编辑标签
const addEditTag = () => {
  const tag = editTagInput.value.trim()
  if (tag && !editForm.value.tags.includes(tag)) {
    editForm.value.tags.push(tag)
    editTagInput.value = ''
  }
}

// 移除编辑标签
const removeEditTag = (index) => {
  editForm.value.tags.splice(index, 1)
}

// 触发编辑文件选择
const triggerEditFileInput = () => {
  editFileInput.value?.click()
}

// 处理编辑文件变化
const handleEditFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('只能上传图片文件!')
    return
  }
  
  // 验证文件大小（2MB）
  if (file.size / 1024 / 1024 > 2) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }
  
  // 读取文件为base64
  const reader = new FileReader()
  reader.onload = (e) => {
    editForm.value.cover = e.target.result
    ElMessage.success('封面上传成功')
  }
  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }
  reader.readAsDataURL(file)
}

// 移除编辑封面
const removeEditCover = () => {
  editForm.value.cover = ''
  // 清除文件输入框的值
  if (editFileInput.value) {
    editFileInput.value.value = ''
  }
}

// 生成编辑简介
const generateEditDescription = async () => {
  if (!editForm.value.title?.trim()) {
    ElMessage.warning('请先填写小说标题')
    return
  }
  
  if (!editForm.value.genre) {
    ElMessage.warning('请先选择小说类型')
    return
  }

  isGeneratingEditDescription.value = true
  try {
    const title = editForm.value.title.trim()
    const genreInfo = genrePresets.value[editForm.value.genre]
    
    // 构建AI提示词
    const prompt = `请为小说《${title}》重新生成一段简介。

小说信息：
- 标题：${title}
- 类型：${genreInfo.name}
- 标签：${genreInfo.tags.join('、')}

要求：
1. 简介长度控制在100-200字之间
2. 突出${genreInfo.name}类型的特色
3. 包含主角、背景设定、核心冲突等元素
4. 语言要吸引人，能激发读者的阅读兴趣
5. 风格要符合${genreInfo.name}小说的特点

请直接输出简介内容，不要包含其他解释文字：`

    // 调用AI API流式生成简介
    const generatedDescription = await apiService.generateTextStream(prompt, {
      maxTokens: null, // 移除token限制
      temperature: 0.8,
      type: 'synopsis'
    }, (chunk, fullContent) => {
      // 实时更新简介内容
      console.log('编辑简介生成流式回调 - chunk:', chunk, 'fullContent长度:', fullContent.length)
      editForm.value.description = fullContent
    })
    
    if (generatedDescription && generatedDescription.trim()) {
      // 流式调用已经在回调中更新了内容，这里只需要显示成功消息
      ElMessage.success('AI简介生成成功！您可以根据需要进行修改')
    } else {
      throw new Error('AI返回的内容为空')
    }
    
  } catch (error) {
    console.error('AI生成简介失败:', error)
    ElMessage.error('AI生成失败，请手动修改简介')
  } finally {
    isGeneratingEditDescription.value = false
  }
}

// 保存小说信息修改
const updateNovelInfo = async () => {
  try {
    await editFormRef.value.validate()
    isSavingEdit.value = true
    
    const index = novels.value.findIndex(n => n.id === editingNovel.value.id)
    if (index > -1) {
      // 更新小说信息
      novels.value[index] = {
        ...novels.value[index],
        title: editForm.value.title,
        genre: editForm.value.genre,
        status: editForm.value.status,
        description: editForm.value.description,
        cover: editForm.value.cover,
        tags: editForm.value.tags,
        updatedAt: new Date()
      }
      
      // 更新类型使用计数（如果类型发生变化）
      if (editingNovel.value.genre !== editForm.value.genre) {
        updateGenreUsageCount(editForm.value.genre)
      }
      
      // 保存到localStorage
      saveNovels()
      
      ElMessage.success('小说信息更新成功')
      showEditDialog.value = false
      resetEditForm()
      
      // 如果当前正在查看详情，更新详情显示
      if (selectedNovel.value && selectedNovel.value.id === editingNovel.value.id) {
        selectedNovel.value = novels.value[index]
      }
    }
  } catch (error) {
    console.error('保存小说信息失败:', error)
  } finally {
    isSavingEdit.value = false
  }
}

const editChapter = (chapter) => {
  ElMessage.info('跳转到章节编辑页面')
}

const generateDescription = async () => {
  if (!createForm.value.title?.trim()) {
    ElMessage.warning('请先填写小说标题')
    return
  }
  
  if (!createForm.value.genre) {
    ElMessage.warning('请先选择小说类型')
    return
  }

  isGeneratingDescription.value = true
  try {
    const title = createForm.value.title.trim()
    const genreInfo = genrePresets.value[createForm.value.genre]
    
    // 构建AI提示词
    const prompt = `请为小说《${title}》生成一段简介。

小说信息：
- 标题：${title}
- 类型：${genreInfo.name}
- 标签：${genreInfo.tags.join('、')}

要求：
1. 简介长度控制在100-200字之间
2. 突出${genreInfo.name}类型的特色
3. 包含主角、背景设定、核心冲突等元素
4. 语言要吸引人，能激发读者的阅读兴趣
5. 风格要符合${genreInfo.name}小说的特点

请直接输出简介内容，不要包含其他解释文字：`

    console.log('开始AI生成简介，提示词:', prompt)
    
    // 调用AI API流式生成简介
    const generatedDescription = await apiService.generateTextStream(prompt, {
      maxTokens: null, // 移除token限制
      temperature: 0.8,
      type: 'synopsis'
    }, (chunk, fullContent) => {
      // 实时更新简介内容
      console.log('简介生成流式回调 - chunk:', chunk, 'fullContent长度:', fullContent.length)
      createForm.value.description = fullContent
    })
    
    if (generatedDescription && generatedDescription.trim()) {
      // 流式调用已经在回调中更新了内容，这里只需要显示成功消息
      ElMessage.success('AI简介生成成功！您可以根据需要进行修改')
    } else {
      throw new Error('AI返回的内容为空')
    }
    
  } catch (error) {
    console.error('AI生成简介失败:', error)
    
    // 根据错误类型提供不同的提示
    let errorMessage = 'AI生成失败'
    if (error.message.includes('API请求失败') || error.message.includes('API Key')) {
      errorMessage = 'AI服务暂时不可用'
    } else if (error.message.includes('网络')) {
      errorMessage = '网络连接失败'
    } else {
      errorMessage = 'AI生成遇到问题'
    }
    
    // 提供备选的模板生成
    ElMessageBox.confirm(
      `${errorMessage}，是否使用本地智能模板生成简介？模板会根据您的标题和类型智能匹配。`, 
      '生成选项', 
      {
        confirmButtonText: '使用智能模板',
        cancelButtonText: '手动填写',
        type: 'info'
      }
    ).then(() => {
      generateDescriptionFromTemplate()
    }).catch(() => {
      // 用户选择手动填写
      ElMessage.info('您可以手动填写简介，或稍后重试AI生成')
    })
    
  } finally {
    isGeneratingDescription.value = false
  }
}

// 备选方案：使用本地模板生成简介
const generateDescriptionFromTemplate = () => {
  const title = createForm.value.title.trim()
  const genreInfo = genrePresets.value[createForm.value.genre]
  
  // 基于类型生成不同风格的简介模板
  const templates = {
    fantasy: [
      `${title}讲述了一个关于修仙与成长的传奇故事。在这个充满灵气与法宝的异世界中，主角将经历重重考验，突破境界桎梏，最终踏上巅峰之路。书中包含丰富的修炼体系、激烈的战斗场面，以及深刻的人性探索。`,
      `这是一部以${title}为名的玄幻巨作。故事背景设定在一个神秘的异世界，那里有着独特的修炼文明和强者为尊的法则。主角将在这个世界中历经磨难，收获友情、爱情与成长，书写属于自己的传奇。`,
      `${title}是一个关于勇气与梦想的修仙传说。在这个弱肉强食的修真世界里，主角凭借坚韧不拔的意志和独特的机缘，从一个普通人逐步成长为绝世强者，期间经历的种种冒险与情感纠葛构成了这部作品的精彩内核。`
    ],
    urban: [
      `${title}是一部现代都市题材的力作，以当代社会为背景，描绘了主角在商场、职场、情场中的精彩人生。故事情节紧贴现实，人物形象鲜活生动，展现了现代都市生活的方方面面。`,
      `这是一个发生在繁华都市中的现代传奇。${title}以独特的视角展现了都市精英的奋斗历程，包含商战智慧、情感纠葛和人生感悟，是一部贴近现实又富有戏剧性的精彩作品。`,
      `${title}讲述了在这个快节奏的现代社会中，主角如何在激烈的竞争中脱颖而出的故事。作品融合了职场智慧、情感描写和社会现象的深度思考，展现了都市生活的真实面貌。`
    ],
    history: [
      `${title}是一部恢弘的历史小说，以真实的历史背景为依托，通过主角的经历展现了那个时代的风云变幻。作品注重历史考证，人物刻画深入，战争场面宏大，是一部兼具文学价值和历史价值的佳作。`,
      `这是一个波澜壮阔的历史传奇。${title}以某个重要历史时期为背景，通过主角的视角展现了朝堂政治、军事战争、民间疾苦等多个层面，构建了一个真实而引人入胜的历史画卷。`,
      `${title}将读者带入了一个充满传奇色彩的历史年代。在那个英雄辈出的时代，主角将经历政治斗争、军事征战、文化碰撞，见证历史的变迁，书写属于自己的历史篇章。`
    ],
    scifi: [
      `${title}是一部想象力丰富的科幻作品，设定在遥远的未来或广袤的宇宙中。故事融合了先进的科技概念、深刻的哲学思考和紧张刺激的冒险情节，展现了人类文明的无限可能。`,
      `这是一个关于未来与科技的宏大叙事。${title}通过主角在星际时代的经历，探讨了科技发展、人性本质、文明演进等深刻主题，是一部兼具娱乐性和思想性的科幻佳作。`,
      `${title}将读者带入了一个充满科技奇迹的未来世界。在这里，人工智能、星际航行、时空穿越等概念成为现实，主角将在这个充满无限可能的宇宙中展开史诗般的冒险。`
    ],
    wuxia: [
      `${title}是一部经典的武侠小说，承载着深厚的江湖文化和武学传统。故事中有着精彩的武功描写、复杂的江湖恩怨、深刻的侠义精神，展现了一个充满豪情与柔情的武林世界。`,
      `这是一个侠骨柔情的江湖传说。${title}以武林为背景，通过主角的成长历程展现了江湖的险恶与温情、武学的精深与传承、侠客的义气与情怀，是一部充满武侠韵味的精彩作品。`,
      `${title}讲述了一个关于武功、情义与正邪的江湖故事。在这个刀光剑影的武林中，主角将学习绝世武功，结交生死兄弟，经历爱恨情仇，最终明悟侠道真谛。`
    ],
    romance: [
      `${title}是一部温馨动人的言情小说，以细腻的笔触描绘了主角们的情感世界。故事情节跌宕起伏，人物情感真挚动人，展现了爱情的美好与复杂，是一部能够触动读者心灵的佳作。`,
      `这是一个关于爱情与成长的美丽故事。${title}通过主角们的相遇、相知、相爱的过程，展现了现代人的情感困惑与追求，用温暖的文字编织了一段动人的爱情童话。`,
      `${title}以爱情为主线，讲述了一段刻骨铭心的情感故事。作品中有欢声笑语，也有离别眼泪，有甜蜜温馨，也有误会波折，最终传达出关于爱情、成长和人生的深刻感悟。`
    ]
  }
  
  // 随机选择一个模板
  const genreTemplates = templates[createForm.value.genre] || templates.fantasy
  const randomTemplate = genreTemplates[Math.floor(Math.random() * genreTemplates.length)]
  
  createForm.value.description = randomTemplate
  ElMessage.success('使用本地模板生成简介成功！')
}

// AI聊天相关方法
const clearCreateChatHistory = () => {
  createChatHistory.value = []
  // 同时清空AI建议
  aiSuggestions.value = {
    title: '',
    genre: '',
    genreKey: '',
    description: ''
  }
}

// 接受AI建议
const acceptSuggestion = (field) => {
  if (field === 'title' && aiSuggestions.value.title) {
    createForm.value.title = aiSuggestions.value.title
    aiSuggestions.value.title = ''
    ElMessage.success('已采用AI建议的标题')
  } else if (field === 'genre' && aiSuggestions.value.genre) {
    // 使用保存的genreKey直接设置
    if (aiSuggestions.value.genreKey) {
      createForm.value.genre = aiSuggestions.value.genreKey
      onGenreChange(aiSuggestions.value.genreKey)
      aiSuggestions.value.genre = ''
      aiSuggestions.value.genreKey = ''
      ElMessage.success('已采用AI建议的类型')
    } else {
      // 兜底逻辑：查找匹配的类型
      for (const [key, value] of Object.entries(genrePresets.value)) {
        if (value.name === aiSuggestions.value.genre) {
          createForm.value.genre = key
          onGenreChange(key)
          aiSuggestions.value.genre = ''
          ElMessage.success('已采用AI建议的类型')
          break
        }
      }
    }
  } else if (field === 'description' && aiSuggestions.value.description) {
    createForm.value.description = aiSuggestions.value.description
    aiSuggestions.value.description = ''
    ElMessage.success('已采用AI建议的简介')
  }
}

const insertQuickPrompt = (basePrompt) => {
  let prompt = basePrompt

  // 根据当前表单状态生成更具体的提示
  if (basePrompt.includes('标题')) {
    if (createForm.value.genre) {
      const genreName = genrePresets.value[createForm.value.genre]?.name || createForm.value.genre
      prompt = `帮我想一个${genreName}小说的标题`
    }
    if (createForm.value.description) {
      prompt += `，简介是：${createForm.value.description.substring(0, 50)}...`
    }
  } else if (basePrompt.includes('类型')) {
    if (createForm.value.title) {
      prompt = `我的小说标题是《${createForm.value.title}》，帮我选择合适的类型`
    }
  } else if (basePrompt.includes('简介')) {
    let context = []
    if (createForm.value.title) {
      context.push(`标题：《${createForm.value.title}》`)
    }
    if (createForm.value.genre) {
      const genreName = genrePresets.value[createForm.value.genre]?.name || createForm.value.genre
      context.push(`类型：${genreName}`)
    }
    if (context.length > 0) {
      prompt = `${context.join('，')}，帮我写一个吸引人的简介`
    }
  } else if (basePrompt.includes('全部填写')) {
    prompt = '我想创作一部小说，请帮我想一个好的标题、选择合适的类型，并写一个吸引人的简介。你可以先问我想写什么类型的故事。'
  }

  createChatInput.value = prompt
}

const sendCreateChatMessage = async () => {
  if (!createChatInput.value.trim()) return

  const userMessage = createChatInput.value.trim()

  // 添加用户消息
  createChatHistory.value.push({
    id: Date.now() + Math.random(),
    content: userMessage,
    isUser: true,
    timestamp: new Date().toLocaleTimeString()
  })

  createChatInput.value = ''
  isCreateChatting.value = true

  // 滚动到底部
  nextTick(() => {
    if (createChatHistoryRef.value) {
      createChatHistoryRef.value.scrollTop = createChatHistoryRef.value.scrollHeight
    }
  })

  try {
    // 检查是否配置了API
    const apiConfig = JSON.parse(localStorage.getItem('apiConfig') || '{}')
    if (!apiConfig.apiKey) {
      throw new Error('请先配置API密钥')
    }

    // 构建上下文信息
    const contextInfo = `
当前表单状态：
- 小说标题：${createForm.value.title || '未填写'}
- 小说类型：${createForm.value.genre || '未选择'}
- 简介：${createForm.value.description || '未填写'}
- 标签：${createForm.value.tags.join('、') || '无'}

可用的小说类型：${Object.keys(genrePresets.value).join('、')}
`

    // 构建AI提示词
    const prompt = `你是一个专业的小说创作助手。用户正在创建一个新小说，需要你帮助填写表单信息。

${contextInfo}

用户问题：${userMessage}

请根据用户的需求，提供具体的建议。如果用户想要你帮忙填写某个字段，请使用以下格式回答：

**建议格式（请严格按照此格式）：**
- 如果建议标题，请用：标题：《具体标题》
- 如果建议类型，请用：类型：具体类型名称
- 如果建议简介，请用：简介：具体简介内容。

**回答要求：**
1. 使用上述标准格式，便于系统自动识别和填写
2. 标题要有创意且符合类型特点（2-20字）
3. 类型必须从以下可用类型中选择：${Object.values(genrePresets.value).map(g => g.name).join('、')}（请使用中文名称）
4. 简介要吸引人且完整（50-200字，以句号结尾）
5. 解释你的建议理由
6. 语气友好专业

**示例回答：**
根据您的描述，我为您推荐：

标题：《星际代码》
类型：科幻
简介：2045年，天才程序员林墨在虚拟现实中发现了来自外星文明的神秘代码。当他试图破解这些代码时，却意外开启了连接两个世界的通道，从此踏上了一段跨越星际的冒险之旅。

这个标题体现了科技与未来的结合，类型选择科幻符合您的设定，简介突出了主角身份和核心冲突。`

    // 调用API
    const response = await apiService.chatWithAI(prompt, createChatHistory.value.slice(-10)) // 只传递最近10条消息作为上下文

    // 添加AI回复
    createChatHistory.value.push({
      id: Date.now() + Math.random(),
      content: response,
      isUser: false,
      timestamp: new Date().toLocaleTimeString()
    })

    // 尝试解析AI回复中的建议并自动填充表单
    parseAIResponseAndFillForm(response)

  } catch (error) {
    console.error('AI聊天失败:', error)

    let errorMessage = '抱歉，AI助手暂时无法回应。'
    if (error.message.includes('API密钥')) {
      errorMessage = '请先在设置中配置API密钥后再使用AI助手。'
    }

    createChatHistory.value.push({
      id: Date.now() + Math.random(),
      content: errorMessage,
      isUser: false,
      timestamp: new Date().toLocaleTimeString()
    })
  } finally {
    isCreateChatting.value = false

    // 滚动到底部
    nextTick(() => {
      if (createChatHistoryRef.value) {
        createChatHistoryRef.value.scrollTop = createChatHistoryRef.value.scrollHeight
      }
    })
  }
}

// 解析AI回复并尝试自动填充表单
const parseAIResponseAndFillForm = (response) => {
  console.log('开始解析AI回复:', response)
  let hasUpdated = false

  // 1. 解析标题 - 支持多种格式
  const titlePatterns = [
    /标题[：:]\s*[《"']([^》"']+)[》"']/g,
    /书名[：:]\s*[《"']([^》"']+)[》"']/g,
    /小说名[：:]\s*[《"']([^》"']+)[》"']/g,
    /建议标题[：:]\s*[《"']([^》"']+)[》"']/g,
    /推荐标题[：:]\s*[《"']([^》"']+)[》"']/g,
    /[《"']([^》"']{2,20})[》"']/g // 通用书名格式
  ]

  for (const pattern of titlePatterns) {
    const matches = [...response.matchAll(pattern)]
    if (matches.length > 0 && !createForm.value.title) {
      const suggestedTitle = matches[0][1].trim()
      console.log('找到标题建议:', suggestedTitle)
      if (suggestedTitle.length >= 2 && suggestedTitle.length <= 20) {
        if (!createForm.value.title) {
          aiSuggestions.value.title = suggestedTitle
          console.log('设置标题建议:', suggestedTitle)
          ElMessage.success(`AI建议标题：${suggestedTitle}，点击"采用"按钮应用`)
          hasUpdated = true
        }
        break
      }
    }
  }

  // 2. 解析类型 - 支持多种表达方式
  const genrePatterns = [
    /类型[：:]\s*([^，。\n\r]+)/g,
    /题材[：:]\s*([^，。\n\r]+)/g,
    /风格[：:]\s*([^，。\n\r]+)/g,
    /这是一?部?\s*([^，。\n\r的]{2,6})\s*小说/g,
    /属于\s*([^，。\n\r的]{2,6})\s*类型/g,
    /建议选择\s*([^，。\n\r的]{2,6})/g
  ]

  for (const pattern of genrePatterns) {
    const matches = [...response.matchAll(pattern)]
    if (matches.length > 0 && !createForm.value.genre) {
      const suggestedGenre = matches[0][1].trim()
      console.log('找到类型建议:', suggestedGenre)

      // 查找匹配的类型
      let matchedGenre = null
      for (const [key, value] of Object.entries(genrePresets.value)) {
        if (value.name.includes(suggestedGenre) ||
            suggestedGenre.includes(value.name) ||
            value.tags.some(tag => suggestedGenre.includes(tag)) ||
            // 支持英文类型名称
            (suggestedGenre.toLowerCase() === 'urban' && value.name.includes('都市')) ||
            (suggestedGenre.toLowerCase() === 'romance' && value.name.includes('言情')) ||
            (suggestedGenre.toLowerCase() === 'fantasy' && value.name.includes('玄幻')) ||
            (suggestedGenre.toLowerCase() === 'scifi' && value.name.includes('科幻'))) {
          matchedGenre = { key, value }
          break
        }
      }

      if (matchedGenre && !createForm.value.genre) {
        aiSuggestions.value.genre = matchedGenre.value.name
        aiSuggestions.value.genreKey = matchedGenre.key // 保存key用于后续选择
        console.log('设置类型建议:', matchedGenre.value.name, '键值:', matchedGenre.key)
        ElMessage.success(`AI建议类型：${matchedGenre.value.name}，点击"采用"按钮应用`)
        hasUpdated = true
      }
      if (hasUpdated) break
    }
  }

  // 3. 解析简介 - 支持多种格式
  const descPatterns = [
    /简介[：:]\s*([^。]+(?:。[^。]*)*。)/g,
    /内容简介[：:]\s*([^。]+(?:。[^。]*)*。)/g,
    /故事简介[：:]\s*([^。]+(?:。[^。]*)*。)/g,
    /剧情[：:]\s*([^。]+(?:。[^。]*)*。)/g,
    /讲述了?\s*([^。]+(?:。[^。]*)*。)/g
  ]

  for (const pattern of descPatterns) {
    const matches = [...response.matchAll(pattern)]
    if (matches.length > 0 && !createForm.value.description) {
      let suggestedDesc = matches[0][1].trim()

      // 清理简介内容
      suggestedDesc = suggestedDesc
        .replace(/^[，。、\s]+/, '') // 移除开头的标点
        .replace(/[，。、\s]+$/, '') // 移除结尾的标点
        .replace(/\s+/g, ' ') // 合并多余空格

      console.log('找到简介建议:', suggestedDesc)
      if (suggestedDesc.length >= 10 && suggestedDesc.length <= 500) {
        if (!createForm.value.description) {
          aiSuggestions.value.description = suggestedDesc
          console.log('设置简介建议:', suggestedDesc)
          ElMessage.success('AI建议简介已生成，点击"采用"按钮应用')
          hasUpdated = true
        }
        break
      }
    }
  }

  // 4. 如果没有找到明确格式，尝试智能提取
  if (!hasUpdated) {
    trySmartExtraction(response)
  }
}

// 智能提取内容
const trySmartExtraction = (response) => {
  // 如果回复中包含明确的建议意图
  if (response.includes('建议') || response.includes('推荐') || response.includes('可以')) {

    // 尝试提取标题建议
    if (!createForm.value.title) {
      const titleHints = ['叫', '名为', '取名', '命名']
      for (const hint of titleHints) {
        const pattern = new RegExp(`${hint}[《"']([^》"']{2,20})[》"']`, 'g')
        const match = pattern.exec(response)
        if (match) {
          createForm.value.title = match[1].trim()
          ElMessage.success(`已自动填入标题：${match[1].trim()}`)
          break
        }
      }
    }

    // 尝试提取类型建议
    if (!createForm.value.genre) {
      const genreKeywords = Object.entries(genrePresets.value)
      for (const [key, value] of genreKeywords) {
        if (response.includes(value.name) ||
            value.tags.some(tag => response.includes(tag))) {
          aiSuggestions.value.genre = value.name
          aiSuggestions.value.genreKey = key
          ElMessage.success(`AI建议类型：${value.name}，点击"采用"按钮应用`)
          break
        }
      }
    }
  }
}

// 生命周期
onMounted(() => {
  // 加载小说数据
  loadNovels()
  // 加载类型数据
  loadGenres()
})

// ==================== 一键生成小说功能 ====================

// 计算属性
const canProceedToGeneration = computed(() => {
  return oneClickForm.value.title &&
         oneClickForm.value.genre &&
         oneClickForm.value.theme &&
         oneClickForm.value.protagonist &&
         oneClickForm.value.background
})

// 重置一键生成对话框
const resetOneClickGenerate = () => {
  currentStep.value = 0
  isGenerating.value = false
  generationProgress.value = 0
  currentGenerationStep.value = ''
  generationLogs.value = []
  generationResult.value = null
  oneClickForm.value = {
    title: '',
    genre: '',
    theme: '',
    protagonist: '',
    background: '',
    chapterCount: 100,  // 默认100章
    characterCount: 20, // 默认20个人物
    generateTypes: ['chapters', 'characters', 'worldview', 'events', 'corpus']
  }
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

// 上一步
const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 开始生成
const startGeneration = async () => {
  isGenerating.value = true
  generationProgress.value = 0
  generationResult.value = {}

  // 计算分批策略
  const batchStrategy = calculateBatchStrategy()

  // 初始化生成日志
  generationLogs.value = createGenerationLogs(batchStrategy)

  try {
    // 步骤1: 准备
    await updateGenerationStep(0, '准备生成环境...', 5)

    let currentLogIndex = 1
    let currentProgress = 5

    // 步骤2: 分批生成章节大纲
    if (oneClickForm.value.generateTypes.includes('chapters')) {
      const chaptersResult = await generateChaptersBatch(batchStrategy.chapters, currentLogIndex, currentProgress)
      generationResult.value.chapters = chaptersResult.total
      currentLogIndex += batchStrategy.chapters.batches
      currentProgress = 30
    }

    // 步骤3: 分批生成人物设定
    if (oneClickForm.value.generateTypes.includes('characters')) {
      const charactersResult = await generateCharactersBatch(batchStrategy.characters, currentLogIndex, currentProgress)
      generationResult.value.characters = charactersResult.total
      currentLogIndex += batchStrategy.characters.batches
      currentProgress = 50
    }

    // 步骤4: 生成世界观设定
    if (oneClickForm.value.generateTypes.includes('worldview')) {
      await updateGenerationStep(currentLogIndex, '正在生成世界观设定...', currentProgress + 10)
      const worldviewResult = await generateWorldview()
      generationResult.value.worldview = worldviewResult
      currentLogIndex++
      currentProgress += 15
    }

    // 步骤5: 生成事件线
    if (oneClickForm.value.generateTypes.includes('events')) {
      await updateGenerationStep(currentLogIndex, '正在生成事件线...', currentProgress + 5)
      const eventsResult = await generateEvents()
      generationResult.value.events = eventsResult
      currentLogIndex++
      currentProgress += 10
    }

    // 步骤6: 生成语料库
    if (oneClickForm.value.generateTypes.includes('corpus')) {
      await updateGenerationStep(currentLogIndex, '正在生成语料库...', currentProgress + 5)
      const corpusResult = await generateCorpus()
      generationResult.value.corpus = corpusResult
      currentLogIndex++
      currentProgress += 10
    }

    // 步骤7: 完成
    await updateGenerationStep(currentLogIndex, '整理和保存数据...', 100)

    // 创建小说项目
    await createNovelFromGeneration()

    ElMessage.success('小说生成完成！')
    currentStep.value = 2

  } catch (error) {
    console.error('一键生成失败:', error)
    ElMessage.error(`生成失败: ${error.message}`)
  } finally {
    isGenerating.value = false
  }
}

// 计算分批策略
const calculateBatchStrategy = () => {
  const chapterCount = oneClickForm.value.chapterCount
  const characterCount = oneClickForm.value.characterCount

  // 章节分批策略
  const chapterBatchSize = 50 // 每批最多50章
  const chapterBatches = Math.ceil(chapterCount / chapterBatchSize)

  // 人物分批策略
  const characterBatchSize = 20 // 每批最多20个人物
  const characterBatches = Math.ceil(characterCount / characterBatchSize)

  return {
    chapters: {
      total: chapterCount,
      batchSize: chapterBatchSize,
      batches: chapterBatches,
      needsBatch: chapterBatches > 1
    },
    characters: {
      total: characterCount,
      batchSize: characterBatchSize,
      batches: characterBatches,
      needsBatch: characterBatches > 1
    }
  }
}

// 创建生成日志
const createGenerationLogs = (batchStrategy) => {
  const logs = [
    { text: '准备生成环境...', completed: false, current: true }
  ]

  // 章节生成日志
  if (oneClickForm.value.generateTypes.includes('chapters')) {
    if (batchStrategy.chapters.needsBatch) {
      for (let i = 1; i <= batchStrategy.chapters.batches; i++) {
        const start = (i - 1) * batchStrategy.chapters.batchSize + 1
        const end = Math.min(i * batchStrategy.chapters.batchSize, batchStrategy.chapters.total)
        logs.push({
          text: `生成章节大纲 (第${start}-${end}章)...`,
          completed: false,
          current: false
        })
      }
    } else {
      logs.push({ text: '生成章节大纲...', completed: false, current: false })
    }
  }

  // 人物生成日志
  if (oneClickForm.value.generateTypes.includes('characters')) {
    if (batchStrategy.characters.needsBatch) {
      for (let i = 1; i <= batchStrategy.characters.batches; i++) {
        const start = (i - 1) * batchStrategy.characters.batchSize + 1
        const end = Math.min(i * batchStrategy.characters.batchSize, batchStrategy.characters.total)
        logs.push({
          text: `生成人物设定 (第${start}-${end}个人物)...`,
          completed: false,
          current: false
        })
      }
    } else {
      logs.push({ text: '生成人物设定...', completed: false, current: false })
    }
  }

  // 其他生成日志
  if (oneClickForm.value.generateTypes.includes('worldview')) {
    logs.push({ text: '生成世界观设定...', completed: false, current: false })
  }
  if (oneClickForm.value.generateTypes.includes('events')) {
    logs.push({ text: '生成事件线...', completed: false, current: false })
  }
  if (oneClickForm.value.generateTypes.includes('corpus')) {
    logs.push({ text: '生成语料库...', completed: false, current: false })
  }

  logs.push({ text: '整理和保存数据...', completed: false, current: false })

  return logs
}

// 更新生成步骤
const updateGenerationStep = async (stepIndex, stepText, progress) => {
  // 更新当前步骤
  if (stepIndex > 0) {
    generationLogs.value[stepIndex - 1].completed = true
    generationLogs.value[stepIndex - 1].current = false
  }

  if (stepIndex < generationLogs.value.length) {
    generationLogs.value[stepIndex].current = true
  }

  currentGenerationStep.value = stepText
  generationProgress.value = progress

  // 模拟生成时间
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

  if (stepIndex < generationLogs.value.length) {
    generationLogs.value[stepIndex].completed = true
    generationLogs.value[stepIndex].current = false
  }
}

// 分批生成章节大纲
const generateChaptersBatch = async (chapterStrategy, startLogIndex, startProgress) => {
  const allChapters = []
  let currentLogIndex = startLogIndex

  if (!chapterStrategy.needsBatch) {
    // 不需要分批，直接生成
    await updateGenerationStep(currentLogIndex, null, startProgress)
    const result = await generateChaptersOutline()
    return { total: result, batches: 1 }
  }

  // 需要分批生成
  for (let batchIndex = 1; batchIndex <= chapterStrategy.batches; batchIndex++) {
    const start = (batchIndex - 1) * chapterStrategy.batchSize + 1
    const end = Math.min(batchIndex * chapterStrategy.batchSize, chapterStrategy.total)

    await updateGenerationStep(currentLogIndex, null, startProgress + (batchIndex - 1) * 20 / chapterStrategy.batches)

    const batchResult = await generateChaptersBatchSingle(start, end, batchIndex, chapterStrategy.batches)
    allChapters.push(batchResult)

    currentLogIndex++
  }

  return { total: allChapters.join('\n\n'), batches: chapterStrategy.batches }
}

// 生成单批章节
const generateChaptersBatchSingle = async (startChapter, endChapter, batchIndex, totalBatches) => {
  const chapterCount = endChapter - startChapter + 1
  const isLongNovel = oneClickForm.value.chapterCount > 50

  let prompt = ''

  if (isLongNovel) {
    prompt = `请为${oneClickForm.value.genre}长篇小说《${oneClickForm.value.title}》生成第${startChapter}-${endChapter}章的详细大纲（共${chapterCount}章）。

这是第${batchIndex}批，总共${totalBatches}批。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}
- 总章节数：${oneClickForm.value.chapterCount}章

${batchIndex === 1 ? `
长篇小说结构要求：
1. 这是故事的${startChapter <= 30 ? '开端阶段' : startChapter <= 60 ? '发展阶段' : startChapter <= 80 ? '高潮阶段' : '结局阶段'}
2. 每章大纲要包含具体的情节发展
3. 注意伏笔的埋设和回收
4. 确保与前后章节的连贯性
5. 为后续剧情发展做好铺垫` : `
续写要求：
1. 与前面章节保持连贯性
2. 推进整体故事发展
3. 深化人物关系和冲突
4. 为后续章节做好铺垫`}

请严格按照以下格式输出：

第${startChapter}章：[章节标题]
大纲：[详细的章节内容描述，包含关键情节点、人物发展、伏笔设置]

第${startChapter + 1}章：[章节标题]
大纲：[详细描述]

继续按此格式生成到第${endChapter}章。`
  } else {
    prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成第${startChapter}-${endChapter}章大纲。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}

要求：
1. 每个章节都有引人入胜的标题
2. 大纲内容详细具体，包含主要情节点
3. 章节之间有逻辑连贯性
4. 推进整体故事发展

请严格按照以下格式输出：

第${startChapter}章：[章节标题]
大纲：[详细的章节内容描述]

继续按此格式生成到第${endChapter}章。`
  }

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: Math.min(4000, chapterCount * 200),
      temperature: 0.8
    })

    return response
  } catch (error) {
    console.error(`生成第${startChapter}-${endChapter}章失败:`, error)
    throw new Error(`第${startChapter}-${endChapter}章生成失败: ${error.message}`)
  }
}

// 生成章节大纲
const generateChaptersOutline = async () => {
  const chapterCount = oneClickForm.value.chapterCount
  const isLongNovel = chapterCount > 50 // 超过50章认为是长篇小说

  let prompt = ''

  if (isLongNovel) {
    // 长篇小说分阶段生成
    prompt = `请为${oneClickForm.value.genre}长篇小说《${oneClickForm.value.title}》制定完整的${chapterCount}章节框架。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}
- 目标字数：预计${Math.floor(chapterCount * 3000)}字（每章约3000字）

长篇小说结构要求：
1. 分为多个大的故事阶段（每20-30章为一个阶段）
2. 每个阶段有明确的主题和目标
3. 包含起承转合的完整结构
4. 人物成长弧线贯穿全文
5. 多条故事线交织发展

请按以下格式生成章节框架：

【第一阶段：开端篇（第1-${Math.min(30, Math.floor(chapterCount * 0.3))}章）】
阶段主题：[这个阶段的主要内容和目标]

第1章：[章节标题]
大纲：[详细描述，包含关键情节点、人物发展、伏笔设置]

第2章：[章节标题]
大纲：[详细描述]

...

【第二阶段：发展篇（第${Math.min(31, Math.floor(chapterCount * 0.3) + 1)}-${Math.floor(chapterCount * 0.7)}章）】
阶段主题：[这个阶段的主要内容和目标]

继续按此格式，将${chapterCount}章分为合理的阶段进行规划。

特别注意：
- 每章大纲要包含具体的情节发展
- 注意伏笔的埋设和回收
- 确保人物关系的复杂性和发展性
- 为后续几百万字的内容做好铺垫`
  } else {
    // 中短篇小说
    prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成${chapterCount}个章节大纲。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}

要求：
1. 每个章节都有引人入胜的标题
2. 大纲内容详细具体，包含主要情节点
3. 章节之间有逻辑连贯性
4. 推进整体故事发展
5. 符合${oneClickForm.value.genre}类型特色

请严格按照以下格式输出：

第1章：[章节标题]
大纲：[详细的章节内容描述]

第2章：[章节标题]
大纲：[详细的章节内容描述]

继续按此格式生成所有${chapterCount}个章节。`
  }

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: 4000,
      temperature: 0.8
    })

    // 简化处理，返回生成的章节数量
    return oneClickForm.value.chapterCount
  } catch (error) {
    console.error('生成章节大纲失败:', error)
    return 0
  }
}

// 分批生成人物设定
const generateCharactersBatch = async (characterStrategy, startLogIndex, startProgress) => {
  const allCharacters = []
  let currentLogIndex = startLogIndex

  if (!characterStrategy.needsBatch) {
    // 不需要分批，直接生成
    await updateGenerationStep(currentLogIndex, null, startProgress)
    const result = await generateCharacters()
    return { total: result, batches: 1 }
  }

  // 需要分批生成
  for (let batchIndex = 1; batchIndex <= characterStrategy.batches; batchIndex++) {
    const start = (batchIndex - 1) * characterStrategy.batchSize + 1
    const end = Math.min(batchIndex * characterStrategy.batchSize, characterStrategy.total)

    await updateGenerationStep(currentLogIndex, null, startProgress + (batchIndex - 1) * 15 / characterStrategy.batches)

    const batchResult = await generateCharactersBatchSingle(start, end, batchIndex, characterStrategy.batches)
    allCharacters.push(batchResult)

    currentLogIndex++
  }

  return { total: allCharacters.join('\n\n'), batches: characterStrategy.batches }
}

// 生成单批人物
const generateCharactersBatchSingle = async (startIndex, endIndex, batchIndex, totalBatches) => {
  const characterCount = endIndex - startIndex + 1
  const isLargeCharacterSet = oneClickForm.value.characterCount > 15

  let prompt = ''

  if (isLargeCharacterSet) {
    prompt = `请为${oneClickForm.value.genre}长篇小说《${oneClickForm.value.title}》生成第${startIndex}-${endIndex}个人物的详细设定（共${characterCount}个人物）。

这是第${batchIndex}批，总共${totalBatches}批。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}
- 总人物数：${oneClickForm.value.characterCount}个

${batchIndex === 1 ? `
长篇小说人物体系要求：
1. 这批人物应该是${startIndex <= 5 ? '主要人物（核心角色）' : startIndex <= 20 ? '重要配角（推动剧情）' : '次要角色（丰富世界观）'}
2. 构建复杂的人物关系网络
3. 每个人物都有成长弧线和发展空间
4. 包含多个对立阵营和利益集团
5. 为长篇连载提供丰富的人物素材` : `
续写要求：
1. 与前面人物保持关系连贯性
2. 丰富整体人物关系网络
3. 为剧情发展提供支撑
4. 保持人物的多样性和代表性`}

请为每个人物提供：
- 姓名
- 角色定位（主角/配角/反派等）
- 年龄和外貌
- 性格特点
- 背景故事
- 能力特长
- 与其他人物的关系
- 在故事中的作用

请按以下格式生成第${startIndex}-${endIndex}个人物：

${startIndex}. [人物姓名] - [角色定位]
   年龄外貌：[详细描述]
   性格特点：[多维度性格分析]
   背景故事：[详细的成长经历和动机]
   能力特长：[具体能力和成长潜力]
   人物关系：[与其他人物的关系]
   故事作用：[在整个故事中的作用]

继续按此格式生成到第${endIndex}个人物。`
  } else {
    prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成第${startIndex}-${endIndex}个人物设定。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}

要求：
1. 包含主角、重要配角、反派等不同类型角色
2. 每个人物都有详细的性格、背景、外貌描述
3. 人物之间有合理的关系网络
4. 符合${oneClickForm.value.genre}类型特色
5. 人物设定要为剧情发展服务

请为每个人物提供：
- 姓名
- 角色定位（主角/配角/反派等）
- 年龄和外貌
- 性格特点
- 背景故事
- 能力特长
- 在故事中的作用`
  }

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: Math.min(3000, characterCount * 300),
      temperature: 0.7
    })

    return response
  } catch (error) {
    console.error(`生成第${startIndex}-${endIndex}个人物失败:`, error)
    throw new Error(`第${startIndex}-${endIndex}个人物生成失败: ${error.message}`)
  }
}

// 生成人物设定
const generateCharacters = async () => {
  const characterCount = oneClickForm.value.characterCount
  const isLargeCharacterSet = characterCount > 15 // 超过15个人物认为是大型人物体系

  let prompt = ''

  if (isLargeCharacterSet) {
    // 大型人物体系，分层级生成
    prompt = `请为${oneClickForm.value.genre}长篇小说《${oneClickForm.value.title}》构建包含${characterCount}个人物的完整人物体系。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}
- 预计章节：${oneClickForm.value.chapterCount}章

长篇小说人物体系要求：
1. 分为主要人物、重要配角、次要角色三个层级
2. 构建复杂的人物关系网络
3. 每个人物都有成长弧线和发展空间
4. 包含多个对立阵营和利益集团
5. 为长篇连载提供丰富的人物素材

请按以下结构生成：

【主要人物（${Math.min(5, Math.floor(characterCount * 0.2))}人）】
这些是贯穿全文的核心角色，包括主角、主要反派、重要伙伴等。

1. [人物姓名] - 主角
   - 年龄外貌：[详细描述]
   - 性格特点：[多维度性格分析]
   - 背景故事：[详细的成长经历和动机]
   - 能力特长：[具体能力和成长潜力]
   - 关系网络：[与其他人物的关系]
   - 成长弧线：[在整个故事中的发展轨迹]

【重要配角（${Math.floor(characterCount * 0.4)}人）】
这些是推动剧情发展的关键角色，有独立的故事线。

【次要角色（${Math.floor(characterCount * 0.4)}人）】
这些是丰富世界观的功能性角色，在特定章节发挥作用。

特别注意：
- 人物之间要有复杂的利益关系和情感纠葛
- 为每个重要人物设计独立的支线剧情
- 确保人物的多样性和代表性
- 为后续剧情发展预留人物发展空间`
  } else {
    // 中等规模人物体系
    prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成${characterCount}个人物设定。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}

要求：
1. 包含主角、重要配角、反派等不同类型角色
2. 每个人物都有详细的性格、背景、外貌描述
3. 人物之间有合理的关系网络
4. 符合${oneClickForm.value.genre}类型特色
5. 人物设定要为剧情发展服务

请为每个人物提供：
- 姓名
- 角色定位（主角/配角/反派等）
- 年龄和外貌
- 性格特点
- 背景故事
- 能力特长
- 在故事中的作用`
  }

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: 3000,
      temperature: 0.7
    })

    return oneClickForm.value.characterCount
  } catch (error) {
    console.error('生成人物设定失败:', error)
    return 0
  }
}

// 生成世界观设定
const generateWorldview = async () => {
  const prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成详细的世界观设定。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 背景：${oneClickForm.value.background}

请生成以下世界观要素：
1. 世界基本设定（时代、地理、社会结构）
2. 力量体系（如果是玄幻/科幻类型）
3. 重要组织机构
4. 历史背景
5. 文化特色
6. 重要地点描述
7. 特殊规则或法则

要求符合${oneClickForm.value.genre}类型特色，为故事发展提供坚实基础。`

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: 2500,
      temperature: 0.7
    })

    return 1
  } catch (error) {
    console.error('生成世界观设定失败:', error)
    return 0
  }
}

// 生成事件线
const generateEvents = async () => {
  const prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成重要事件线。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 主角：${oneClickForm.value.protagonist}
- 背景：${oneClickForm.value.background}

请生成以下类型的事件：
1. 主线事件（推动主要剧情发展）
2. 支线事件（丰富故事内容）
3. 转折事件（改变故事走向）
4. 高潮事件（故事的重要节点）
5. 背景事件（世界观相关）

每个事件包含：
- 事件名称
- 发生时间/章节
- 参与人物
- 事件描述
- 对故事的影响`

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: 2000,
      temperature: 0.7
    })

    return 8
  } catch (error) {
    console.error('生成事件线失败:', error)
    return 0
  }
}

// 生成语料库
const generateCorpus = async () => {
  const prompt = `请为${oneClickForm.value.genre}小说《${oneClickForm.value.title}》生成语料库素材。

小说信息：
- 标题：${oneClickForm.value.title}
- 类型：${oneClickForm.value.genre}
- 主题：${oneClickForm.value.theme}
- 背景：${oneClickForm.value.background}

请生成以下类型的语料：
1. 环境描写片段（${oneClickForm.value.genre}风格）
2. 人物对话示例
3. 动作场面描述
4. 心理活动描写
5. 专业术语词汇
6. 常用句式模板

要求：
- 符合${oneClickForm.value.genre}类型的文风特色
- 可以直接用于写作参考
- 语言生动有感染力
- 涵盖不同写作场景`

  try {
    const response = await apiService.generateTextStream(prompt, {
      maxTokens: 2000,
      temperature: 0.8
    })

    return 6
  } catch (error) {
    console.error('生成语料库失败:', error)
    return 0
  }
}

// 从生成结果创建小说
const createNovelFromGeneration = async () => {
  const newNovel = {
    id: Date.now(),
    title: oneClickForm.value.title,
    description: `${oneClickForm.value.theme}主题的${oneClickForm.value.genre}小说。${oneClickForm.value.protagonist}`,
    genre: oneClickForm.value.genre,
    tags: [oneClickForm.value.genre, oneClickForm.value.theme],
    status: 'writing',
    wordCount: 0,
    chapterCount: generationResult.value.chapters || 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    cover: '',
    isAIGenerated: true,
    generationData: {
      theme: oneClickForm.value.theme,
      protagonist: oneClickForm.value.protagonist,
      background: oneClickForm.value.background,
      generatedTypes: oneClickForm.value.generateTypes,
      generationResult: generationResult.value
    }
  }

  // 添加到小说列表
  novels.value.unshift(newNovel)

  // 保存到localStorage
  saveNovels()

  return newNovel
}

// 完成并跳转到编辑器
const finishAndJumpToEditor = async () => {
  try {
    // 找到刚创建的小说
    const latestNovel = novels.value[0]

    ElMessage.success('即将跳转到编辑器...')
    showOneClickGenerateDialog.value = false
    resetOneClickGenerate()

    // 跳转到编辑页面
    setTimeout(() => {
      router.push(`/writer?novelId=${latestNovel.id}`)
    }, 1000)

  } catch (error) {
    console.error('跳转失败:', error)
    ElMessage.error('跳转失败，请手动进入编辑器')
  }
}
</script>

<style scoped>
.novel-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.filter-left {
  display: flex;
  gap: 15px;
}

.novels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.novel-card {
  height: 100%;
}

.novel-item {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.novel-item :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.novel-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.novel-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.novel-status {
  position: absolute;
  top: 10px;
  right: 10px;
}

.novel-info {
  flex: 1;
  padding: 15px;
}

.novel-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.novel-description {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.novel-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

.novel-genre {
  margin-bottom: 15px;
}

.novel-actions {
  display: flex;
  gap: 8px;
  padding: 0 15px 15px;
  margin-top: auto;
}

.empty-state {
  padding: 60px 0;
}

.cover-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 160px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-uploader:hover {
  border-color: #409eff;
  background-color: #f8f9fa;
}

.cover-uploader-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

.cover-uploader-icon {
  font-size: 24px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: #8c939d;
  line-height: 1.2;
}

.cover-preview {
  width: 120px;
  height: 160px;
  object-fit: cover;
  display: block;
  border-radius: 6px;
}

.cover-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.cover-actions {
  display: flex;
  gap: 8px;
}

.tags-display {
  margin-top: 10px;
}

.tags-display .el-tag {
  margin: 2px 4px 2px 0;
}

.description-input-group {
  position: relative;
}

.ai-generate-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.generate-tip {
  font-size: 12px;
  color: #6c757d;
}

/* 创建小说对话框布局 */
.create-novel-container {
  display: flex;
  gap: 20px;
  height: 600px;
}

.form-section {
  flex: 1;
  overflow-y: auto;
}

.ai-assistant-section {
  width: 350px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #ebeef5;
  padding-left: 20px;
}

.ai-assistant-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.ai-chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 12px;
  max-height: 400px;
}

.chat-message {
  margin-bottom: 12px;
  animation: fadeIn 0.3s ease-in;
}

.chat-message.user-message .message-content {
  background: #409eff;
  color: white;
  margin-left: 20px;
  border-radius: 12px 12px 4px 12px;
}

.chat-message.ai-message .message-content {
  background: white;
  color: #303133;
  margin-right: 20px;
  border-radius: 12px 12px 12px 4px;
  border: 1px solid #e4e7ed;
}

.message-content {
  padding: 8px 12px;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-time {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
  text-align: right;
}

.chat-message.ai-message .message-time {
  text-align: left;
}

.empty-chat {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px;
  line-height: 1.6;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  padding: 8px 12px;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: #909399;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-input {
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
}

.chat-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.quick-actions {
  display: flex;
  gap: 4px;
}

.quick-actions .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

/* AI建议样式 */
.ai-suggestion {
  margin-top: 8px;
  padding: 10px 14px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
  border-radius: 8px;
  border: 1px solid #4caf50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 10px;
  animation: slideIn 0.3s ease-out;
  position: relative;
}

.ai-suggestion::before {
  content: "🤖";
  position: absolute;
  left: -8px;
  top: -8px;
  background: #4caf50;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.suggestion-label {
  font-size: 12px;
  color: #2e7d32;
  font-weight: 600;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  font-size: 13px;
  color: #1b5e20;
  line-height: 1.4;
  font-weight: 500;
}

.description-suggestion {
  max-height: 60px;
  overflow-y: auto;
  padding-right: 4px;
}

.description-suggestion::-webkit-scrollbar {
  width: 4px;
}

.description-suggestion::-webkit-scrollbar-thumb {
  background: #81c784;
  border-radius: 2px;
}

.ai-suggestion .el-button {
  background: #4caf50;
  border-color: #4caf50;
  color: white;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.ai-suggestion .el-button:hover {
  background: #45a049;
  border-color: #45a049;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.novel-details {
  max-height: 600px;
  overflow-y: auto;
}

.details-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.details-cover {
  flex-shrink: 0;
}

.details-cover img {
  width: 120px;
  height: 160px;
  object-fit: cover;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.details-info {
  flex: 1;
}

.details-info h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.details-description {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.6;
}

.details-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meta-label {
  font-weight: 500;
  color: #303133;
  min-width: 80px;
}

.chapters-list {
  max-height: 300px;
  overflow-y: auto;
}

.chapter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.chapter-info h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #303133;
}

.chapter-info p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.writing-records {
  max-height: 300px;
  overflow-y: auto;
}

.record-item {
  display: flex;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.record-date {
  flex-shrink: 0;
  font-size: 12px;
  color: #909399;
  min-width: 80px;
}

.record-content {
  flex: 1;
}

.record-stats {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #606266;
  margin-bottom: 5px;
}

.record-note {
  font-size: 12px;
  color: #909399;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.image-placeholder i {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 一键生成按钮样式 */
.one-click-generate-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.one-click-generate-btn:hover {
  background: linear-gradient(135deg, #85ce61, #67c23a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

/* 一键生成对话框样式 */
.one-click-generate-container {
  max-height: 600px;
  overflow-y: auto;
}

.generation-steps {
  margin-bottom: 30px;
}

.step-content {
  min-height: 400px;
}

.one-click-form .form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.generation-progress {
  text-align: center;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 600;
  color: #409eff;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-text {
  margin-top: 12px;
  color: #606266;
  font-size: 14px;
}

.generation-log {
  margin-top: 20px;
  text-align: left;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: #909399;
  transition: color 0.3s;
}

.log-item.completed {
  color: #67c23a;
}

.log-item.current {
  color: #409eff;
  font-weight: 600;
}

.generation-result .result-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 长篇小说提示样式 */
.long-novel-tip {
  margin-top: 20px;
}

.tip-content {
  line-height: 1.6;
}

.tip-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.tip-content li {
  margin: 5px 0;
}

.tip-highlight {
  margin-top: 10px;
  font-weight: 600;
  color: #409eff;
}

/* 分批生成提示样式 */
.batch-tip {
  margin-top: 15px;
}

.batch-tip .tip-content {
  line-height: 1.6;
}

.batch-tip .tip-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.batch-tip .tip-content li {
  margin: 5px 0;
}

.batch-tip .tip-highlight {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .filter-content {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-left {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .novels-grid {
    grid-template-columns: 1fr;
  }
  
  .details-header {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>