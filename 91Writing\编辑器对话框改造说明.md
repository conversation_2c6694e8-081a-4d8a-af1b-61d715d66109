# 编辑器对话框改造说明

## 改造目标
将编辑器页面的标签页功能改为对话框形式，提升用户体验和界面整洁度。

## 改造内容

### 1. 标签栏改造
**原来的设计：**
- 使用 `el-tabs` 组件
- 5个标签页：编辑、人物、世界观、语料库、事件线

**改造后的设计：**
- 使用按钮组替代标签页
- 编辑保持原有功能，其他4个功能改为对话框

### 2. 新增对话框

#### 人物管理对话框 (`showCharactersDialog`)
- **宽度**: 1000px
- **功能**: 
  - 显示所有角色的网格布局
  - 支持新增、编辑、删除角色
  - 支持AI批量生成角色
- **特色**: 
  - 卡片式布局，每个角色显示头像、基本信息、标签
  - 悬停效果和操作菜单

#### 世界观管理对话框 (`showWorldviewDialog`)
- **宽度**: 1000px
- **功能**:
  - 显示所有世界观设定
  - 支持新增、编辑、复制、删除设定
  - 支持AI生成世界观
- **特色**:
  - 网格布局，显示设定标题、类型、描述
  - 区分AI生成和手动创建的内容

#### 语料库管理对话框 (`showCorpusDialog`)
- **宽度**: 1000px
- **功能**:
  - 显示所有语料数据
  - 支持新增、编辑、删除语料
- **特色**:
  - 按类型分类显示
  - 内容预览功能

#### 事件线管理对话框 (`showEventsDialog`)
- **宽度**: 1000px
- **功能**:
  - 时间线形式显示所有事件
  - 支持新增、编辑、删除事件
- **特色**:
  - 时间线布局，带有连接线和标记点
  - 显示章节关联和时间信息

### 3. 界面优化

#### 按钮样式
```css
.main-tabs-buttons {
  display: flex;
  gap: 8px;
  padding: 12px 0;
}

.tab-button {
  border-radius: 6px;
  font-weight: 500;
}
```

#### 对话框样式
- 统一的管理头部布局
- 网格/时间线布局
- 悬停效果和过渡动画
- 空状态提示

### 4. 交互改进

#### 原来的交互：
- 点击标签页切换内容
- 内容固定在左侧面板

#### 改造后的交互：
- 点击按钮打开对应对话框
- 对话框覆盖显示，不影响编辑器
- 可以同时打开多个对话框进行操作

### 5. 技术实现

#### 状态管理
```javascript
// 对话框状态
const showCharactersDialog = ref(false)
const showWorldviewDialog = ref(false)
const showCorpusDialog = ref(false)
const showEventsDialog = ref(false)
```

#### 按钮绑定
```vue
<el-button @click="showCharactersDialog = true" class="tab-button">
  👥 人物
</el-button>
```

#### 对话框结构
```vue
<el-dialog v-model="showCharactersDialog" title="人物管理" width="1000px" top="5vh">
  <div class="characters-management">
    <!-- 管理头部 -->
    <div class="management-header">...</div>
    <!-- 内容网格 -->
    <div class="characters-grid">...</div>
    <!-- 空状态 -->
    <div class="empty-state">...</div>
  </div>
</el-dialog>
```

## 优势

### 1. 界面更整洁
- 左侧面板专注于章节列表
- 右侧编辑器获得更多空间
- 减少界面复杂度

### 2. 操作更灵活
- 可以在编辑时快速查看和管理其他内容
- 对话框可以独立操作，不影响编辑状态
- 支持同时管理多种内容

### 3. 用户体验更好
- 大尺寸对话框提供更好的内容展示
- 网格布局更适合浏览和管理
- 统一的操作模式

### 4. 扩展性更强
- 易于添加新的管理功能
- 对话框可以独立优化
- 便于移动端适配

## 测试建议

1. **功能测试**
   - 验证所有按钮能正确打开对话框
   - 测试对话框内的增删改查功能
   - 确认对话框关闭后状态正确

2. **界面测试**
   - 检查对话框布局在不同屏幕尺寸下的表现
   - 验证悬停效果和动画
   - 确认空状态显示正确

3. **交互测试**
   - 测试同时打开多个对话框的情况
   - 验证对话框与编辑器的交互
   - 确认数据同步正确

## 后续优化

1. **响应式设计**: 针对移动端优化对话框布局
2. **搜索功能**: 在各个管理对话框中添加搜索和筛选
3. **批量操作**: 支持批量选择和操作
4. **拖拽排序**: 支持拖拽调整顺序
5. **导入导出**: 支持数据的导入导出功能
