# 章节排序修复说明

## 问题描述
在章节列表中，章节的显示顺序混乱，"第1章"、"第2章"、"第3章"等没有按照正确的数字顺序排列，导致用户体验不佳。

## 问题原因
原来的章节列表直接使用`chapters.value`数组，没有进行任何排序处理。章节的显示顺序完全依赖于它们在数组中的存储顺序，这可能受到以下因素影响：
- 章节创建的时间顺序
- 数据加载的顺序
- 用户操作的顺序

## 解决方案

### 1. 创建排序计算属性
新增了`sortedChapters`计算属性，实现智能章节排序：

```javascript
const sortedChapters = computed(() => {
  return [...chapters.value].sort((a, b) => {
    // 提取章节标题中的数字进行排序
    const getChapterNumber = (title) => {
      const match = title.match(/第?(\d+)章?|章节(\d+)|(\d+)/)
      if (match) {
        return parseInt(match[1] || match[2] || match[3])
      }
      return 999999 // 没有数字的章节排在最后
    }
    
    const numA = getChapterNumber(a.title)
    const numB = getChapterNumber(b.title)
    
    // 多级排序逻辑
    if (numA !== numB) return numA - numB
    if (timeA !== timeB) return timeA - timeB
    return a.id - b.id
  })
})
```

### 2. 智能数字提取
排序算法能够识别多种章节标题格式：
- `第1章` → 提取数字 1
- `第2章 标题` → 提取数字 2  
- `章节3` → 提取数字 3
- `4` → 提取数字 4
- `第五章` → 无数字，排在最后

### 3. 多级排序策略
1. **主要排序**: 按章节号数字大小排序
2. **次要排序**: 章节号相同时按创建时间排序
3. **最终排序**: 时间也相同时按ID排序

### 4. 更新所有相关组件
将所有使用章节列表的地方都更新为使用排序后的列表：

#### 章节列表显示
```html
<div v-for="(chapter, index) in sortedChapters" :key="chapter.id">
```

#### 上下文章节列表
```javascript
const availableContextChapters = computed(() => {
  return sortedChapters.value.filter(ch => {
    return ch.description || (ch.content && ch.content.trim())
  })
})
```

#### 章节索引计算
```javascript
const getChapterIndex = (chapter) => {
  return sortedChapters.value.findIndex(ch => ch.id === chapter.id) + 1
}
```

## 修复效果

### 修复前
```
章节列表显示顺序：
- 第3章
- 第1章  
- 第5章
- 第2章
- 第4章
```

### 修复后
```
章节列表显示顺序：
- 第1章
- 第2章
- 第3章
- 第4章
- 第5章
```

## 技术细节

### 正则表达式解析
使用正则表达式`/第?(\d+)章?|章节(\d+)|(\d+)/`来提取章节号：
- `第?(\d+)章?` - 匹配"第X章"或"X章"格式
- `章节(\d+)` - 匹配"章节X"格式  
- `(\d+)` - 匹配纯数字格式

### 性能优化
- 使用`computed`属性，只在`chapters.value`变化时重新计算
- 排序算法时间复杂度为O(n log n)
- 避免在每次渲染时重复排序

### 兼容性保证
- 保持原有的章节数据结构不变
- 不影响章节的增删改操作
- 向后兼容各种章节标题格式

## 相关功能更新

### 1. 章节索引显示
所有显示章节序号的地方都会显示正确的顺序：
- 章节列表中的序号
- 上下文选择中的序号
- AI生成时的章节引用

### 2. 章节导航
用户点击章节时，能够按照正确的逻辑顺序浏览：
- 上一章/下一章功能正确
- 章节跳转逻辑正确

### 3. AI生成上下文
AI生成内容时引用的章节顺序正确：
- 前文概要按正确顺序
- 章节关联按逻辑顺序

## 测试验证

### 测试用例
1. **基本排序**: 创建"第1章"、"第3章"、"第2章"，验证显示为1、2、3
2. **混合格式**: 测试"第1章"、"章节2"、"3"等不同格式
3. **无数字章节**: 测试"序章"、"尾声"等无数字章节排在最后
4. **相同数字**: 测试多个"第1章"按创建时间排序

### 验证方法
1. 在浏览器中查看章节列表顺序
2. 检查章节索引显示是否正确
3. 验证AI生成时的章节引用顺序
4. 测试章节导航功能

## 注意事项

### 1. 数据迁移
现有的章节数据无需迁移，排序逻辑会自动处理。

### 2. 用户习惯
用户可能需要适应新的排序逻辑，但这是更符合直觉的改进。

### 3. 特殊情况
- 如果章节标题完全没有数字，会排在最后
- 相同章节号的章节按创建时间排序
- 建议用户使用标准的章节命名格式

## 后续优化

### 1. 手动排序
可以考虑添加手动拖拽排序功能，让用户自定义章节顺序。

### 2. 智能重命名
可以添加批量重命名功能，自动规范化章节标题格式。

### 3. 排序设置
可以添加用户设置，允许选择不同的排序方式（按标题、按时间、按自定义等）。

这个修复确保了章节列表始终按照逻辑顺序显示，大大改善了用户的使用体验！
