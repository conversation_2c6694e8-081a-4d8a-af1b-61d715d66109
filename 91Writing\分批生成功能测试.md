# 分批生成功能测试指南

## 🎯 功能概述

分批生成功能是为了解决大型长篇小说（几百万字）创作时，单次AI请求内容过多导致的问题而设计的智能解决方案。

## 🔧 分批策略

### 章节分批策略
- **触发条件**：章节数量 > 50章
- **分批大小**：每批最多50章
- **分批逻辑**：
  ```
  总章节数：300章
  分批数量：Math.ceil(300 / 50) = 6批
  第1批：第1-50章
  第2批：第51-100章
  第3批：第101-150章
  第4批：第151-200章
  第5批：第201-250章
  第6批：第251-300章
  ```

### 人物分批策略
- **触发条件**：人物数量 > 20个
- **分批大小**：每批最多20个人物
- **分批逻辑**：
  ```
  总人物数：60个
  分批数量：Math.ceil(60 / 20) = 3批
  第1批：第1-20个人物（主要人物）
  第2批：第21-40个人物（重要配角）
  第3批：第41-60个人物（次要角色）
  ```

## 📱 界面提示

### 长篇小说提示（≥100章）
显示内容：
- 字数规划：300章 × 3000字 ≈ 90万字
- 更新节奏：建议每日1-2章
- 故事结构：分阶段发展模式
- 人物配置：支撑复杂关系网络
- 世界观扩展：预留发展空间

### 分批生成提示（>50章或>20人物）
显示内容：
- 章节分批：300章分为6批生成
- 人物分批：60个人物分为3批生成
- 提高成功率：避免单次请求过大
- 详细进度：实时查看每批状态
- 错误恢复：单批失败可单独重试

## 🚀 生成流程

### 1. 准备阶段
- 计算分批策略
- 创建详细的生成日志
- 初始化进度追踪

### 2. 章节分批生成
```javascript
// 示例：300章分6批生成
for (let batchIndex = 1; batchIndex <= 6; batchIndex++) {
  const start = (batchIndex - 1) * 50 + 1
  const end = Math.min(batchIndex * 50, 300)
  
  // 生成第start-end章
  await generateChaptersBatchSingle(start, end, batchIndex, 6)
}
```

### 3. 人物分批生成
```javascript
// 示例：60个人物分3批生成
for (let batchIndex = 1; batchIndex <= 3; batchIndex++) {
  const start = (batchIndex - 1) * 20 + 1
  const end = Math.min(batchIndex * 20, 60)
  
  // 生成第start-end个人物
  await generateCharactersBatchSingle(start, end, batchIndex, 3)
}
```

## 📊 进度显示

### 生成日志示例（300章 + 60人物）
```
✅ 准备生成环境...
🔄 生成章节大纲 (第1-50章)...
⏳ 生成章节大纲 (第51-100章)...
⏳ 生成章节大纲 (第101-150章)...
⏳ 生成章节大纲 (第151-200章)...
⏳ 生成章节大纲 (第201-250章)...
⏳ 生成章节大纲 (第251-300章)...
⏳ 生成人物设定 (第1-20个人物)...
⏳ 生成人物设定 (第21-40个人物)...
⏳ 生成人物设定 (第41-60个人物)...
⏳ 生成世界观设定...
⏳ 生成事件线...
⏳ 生成语料库...
⏳ 整理和保存数据...
```

## 🎨 提示词优化

### 长篇章节提示词特点
- 包含阶段信息（开端/发展/高潮/结局）
- 强调连贯性和伏笔设置
- 考虑整体故事结构
- 为后续章节做铺垫

### 分层人物提示词特点
- 区分人物层级（主要/重要/次要）
- 构建复杂关系网络
- 预留成长空间
- 支撑长篇发展

## 🧪 测试用例

### 测试用例1：中等长篇小说
- **设置**：150章，30个人物
- **预期分批**：
  - 章节：3批（1-50，51-100，101-150）
  - 人物：2批（1-20，21-30）
- **生成日志**：8个步骤

### 测试用例2：超长篇小说
- **设置**：500章，80个人物
- **预期分批**：
  - 章节：10批（每批50章）
  - 人物：4批（每批20个人物）
- **生成日志**：17个步骤

### 测试用例3：小型小说（不分批）
- **设置**：30章，15个人物
- **预期分批**：
  - 章节：不分批（直接生成）
  - 人物：不分批（直接生成）
- **生成日志**：7个步骤

## ⚡ 性能优化

### 1. 请求优化
- 根据内容量动态调整maxTokens
- 章节：每章约200 tokens
- 人物：每个人物约300 tokens

### 2. 错误处理
- 单批失败不影响其他批次
- 提供详细的错误信息
- 支持失败批次的重试

### 3. 用户体验
- 实时进度更新
- 详细的状态显示
- 清晰的分批策略说明

## 🎯 使用建议

### 对于几百万字小说创作：

#### 1. 合理设置参数
- **章节数**：300-1000章
- **人物数**：50-100个
- **每章字数**：3000-5000字

#### 2. 分批策略选择
- **章节分批**：50章/批（约15万字）
- **人物分批**：20个/批（便于管理）

#### 3. 创作节奏规划
- **生成阶段**：一次性生成完整框架
- **创作阶段**：按批次逐步完善内容
- **修改阶段**：分批次进行内容调整

#### 4. 质量控制
- 每批生成后及时检查质量
- 确保批次间的连贯性
- 根据需要调整后续批次的提示词

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 单批生成失败
- **现象**：某一批生成失败，其他批次正常
- **解决**：系统会自动跳过失败批次，继续后续生成
- **建议**：生成完成后可单独重试失败的批次

#### 2. 内容连贯性问题
- **现象**：不同批次间内容不够连贯
- **解决**：在提示词中强调与前后批次的连贯性
- **建议**：生成后进行人工审核和调整

#### 3. 进度显示异常
- **现象**：进度条或日志显示不正确
- **解决**：刷新页面重新开始生成
- **建议**：确保网络连接稳定

## 📈 效果评估

### 成功指标
- ✅ 所有批次成功生成
- ✅ 内容质量符合预期
- ✅ 批次间连贯性良好
- ✅ 总体结构合理完整

### 优化方向
- 🔧 根据用户反馈调整分批大小
- 🔧 优化提示词模板
- 🔧 改进错误处理机制
- 🔧 增强用户体验

通过分批生成功能，用户可以轻松创建几百万字的长篇小说框架，为后续的深度创作奠定坚实基础！
